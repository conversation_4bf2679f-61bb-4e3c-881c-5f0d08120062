import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, Matches, IsArray, IsNumber } from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'Password123',
    description: 'User password',
    minLength: 6
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'User full name',
    required: false
  })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number with country code',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format (e.g., +1234567890)'
  })
  phoneNumber?: string;

  @ApiProperty({ 
    example: [1, 2], 
    description: 'Array of diet IDs',
    required: false,
    type: [Number]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  diets?: number[];

  @ApiProperty({ 
    example: [1, 2], 
    description: 'Array of allergy IDs',
    required: false,
    type: [Number]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  allergies?: number[];
}

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'Password123',
    description: 'User password'
  })
  @IsString()
  password: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token'
  })
  @IsString()
  refresh_token: string;
}

export class TokenResponse {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token'
  })
  access_token: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT refresh token'
  })
  refresh_token: string;

  @ApiProperty({
    example: 3600,
    description: 'Access token expiration time in seconds'
  })
  expires_in: number;

  @ApiProperty({
    example: 604800,
    description: 'Refresh token expiration time in seconds'
  })
  refresh_token_expires_in: number;
}

export class ForgotPasswordDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the user'
  })
  @IsEmail()
  email: string;
}

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Reset password token received via email'
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'New password',
    minLength: 8
  })
  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number'
  })
  password: string;
} 