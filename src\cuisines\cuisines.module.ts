import { Module } from '@nestjs/common';
import { CuisinesService } from './cuisines.service';
import { <PERSON>uisinesController } from './cuisines.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { UploadModule } from '../common/modules/upload.module';

@Module({
  imports: [PrismaModule, UploadModule],
  controllers: [CuisinesController],
  providers: [CuisinesService],
  exports: [CuisinesService],
})
export class CuisinesModule {} 