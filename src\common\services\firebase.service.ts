import { Injectable, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { PrismaService } from '../../prisma/prisma.service';
import * as serviceAccount from '../../config/firebase-service-account.json';
import { Prisma } from '@prisma/client';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private senderId: string;

  constructor(private prisma: PrismaService) {}

  async onModuleInit() {
    // Get sender ID from general settings
    const setting = await this.prisma.generalSetting.findFirst({
      where: {
        key: 'firebase_sender_id'
      }
    });

    this.senderId = setting?.value;

    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: serviceAccount.project_id,
          clientEmail: serviceAccount.client_email,
          privateKey: serviceAccount.private_key
        }),
      });
    }
  }

  async sendNotificationToUsers(notification: { 
    title: string;
    description: string;
    image?: string;
    users?: number[];
    all?: boolean;
  }) {
    try {
      // Get all user IDs if all is true
      const userIds = notification.all 
        ? (await this.prisma.user.findMany({
            select: { id: true },
            where: { deletedAt: null }
          })).map(user => user.id)
        : notification.users?.map(id => BigInt(id)) || [];

      // Save notification to database
      await this.prisma.notification.create({
        data: {
          title: notification.title,
          description: notification.description,
          image: notification.image,
          users: userIds,
          isAll: notification.all || false,
        },
      });

      // Get users based on criteria
      const users = await this.prisma.user.findMany({
        where: {
          ...(notification.all ? {} : notification.users ? {
            id: {
              in: notification.users.map(id => BigInt(id))
            }
          } : {}),
          fcmToken: { not: null }
        },
        select: {
          fcmToken: true
        }
      });

      const tokens = users.map(user => user.fcmToken!);
      if (!tokens.length) return { success: false, message: 'No valid FCM tokens found' };

      const message: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notification.title,
          body: notification.description,
          imageUrl: notification.image || undefined
        }
      };

      const response = await admin.messaging().sendEachForMulticast(message);

      return {
        success: true,
        results: response.responses
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async resendNotification(id: number) {
    try {
      // Get the existing notification
      const notification = await this.prisma.notification.findUnique({
        where: { id: BigInt(id) }
      });

      if (!notification) {
        return { success: false, message: 'Notification not found' };
      }

      // Get user IDs based on isAll flag
      const userIds = notification.isAll
        ? (await this.prisma.user.findMany({
            select: { id: true },
            where: { deletedAt: null }
          })).map(user => user.id)
        : notification.users;

      // Save new notification to database
      const newNotification = await this.prisma.notification.create({
        data: {
          title: notification.title,
          description: notification.description,
          image: notification.image,
          users: userIds,
          isAll: notification.isAll,
        },
      });

      // Get users with FCM tokens
      const users = await this.prisma.user.findMany({
        where: {
          id: { in: userIds },
          fcmToken: { not: null }
        },
        select: {
          fcmToken: true
        }
      });

      const tokens = users.map(user => user.fcmToken!);
      if (!tokens.length) return { success: false, message: 'No valid FCM tokens found' };

      const message: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notification.title,
          body: notification.description,
          imageUrl: notification.image || undefined
        }
      };

      const response = await admin.messaging().sendEachForMulticast(message);

      return {
        success: true,
        notificationId: newNotification.id,
        results: response.responses
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getNotifications({ page = 1, limit = 10 }) {
    try {
      const skip = (page - 1) * limit;

      const [notifications, total] = await Promise.all([
        this.prisma.notification.findMany({
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc'
          }
        }),
        this.prisma.notification.count()
      ]);

      return {
        success: true,
        data: notifications,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
} 