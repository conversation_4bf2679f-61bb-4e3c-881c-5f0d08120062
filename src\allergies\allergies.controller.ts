import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AllergiesService } from './allergies.service';
import { CreateAllergyDto, UpdateAllergyDto } from './dto/allergy.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('Allergies')
@Controller('allergies')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class AllergiesController {
  constructor(private readonly allergiesService: AllergiesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new allergy (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Allergy created successfully' })
  create(@Body() dto: CreateAllergyDto) {
    return this.allergiesService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all allergies (USER, ADMIN, SUPER_ADMIN)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted allergies. If false, returns only active allergies.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Return paginated allergies',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Allergy' }
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            hasNextPage: { type: 'boolean' },
            hasPreviousPage: { type: 'boolean' }
          }
        }
      }
    }
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: string,
  ) {
    return this.allergiesService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted: onlyDeleted === 'true'
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get allergy by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the allergy' })
  findOne(@Param('id') id: string) {
    return this.allergiesService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update allergy (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Allergy updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateAllergyDto) {
    return this.allergiesService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete allergy (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Allergy deleted successfully' })
  remove(@Param('id') id: string) {
    return this.allergiesService.remove(toBigInt(id));
  }
} 