import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Prisma } from '@prisma/client';
import { toBigInt } from '../common/utils/bigint.util';

@Injectable()
export class CategoriesService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateCategoryDto) {
    const exists = await this.prisma.category.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en
          ? `Category with English name "${dto.name_en}" already exists`
          : `Category with French name "${dto.name_fr}" already exists`
      );
    }

    return this.prisma.category.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.CategoryWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } },
          { description_en: { contains: search, mode: 'insensitive' } },
          { description_fr: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.category.count({ where });

    const categories = await this.prisma.category.findMany({
      where,
      orderBy: { id: order },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      items: categories,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const category = await this.prisma.category.findFirst({
      where: { 
        id,
        deletedAt: null
      }
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  async update(id: bigint, dto: UpdateCategoryDto) {
    const category = await this.prisma.category.findFirst({
      where: { id }
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr) {
      const exists = await this.prisma.category.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en
            ? `Category with English name "${dto.name_en}" already exists`
            : `Category with French name "${dto.name_fr}" already exists`
        );
      }
    }

    return this.prisma.category.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.category.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }
} 