/*
  Warnings:

  - You are about to drop the column `name` on the `ingredients` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `units` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name_en]` on the table `ingredients` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_fr]` on the table `ingredients` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_en]` on the table `units` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_fr]` on the table `units` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `name_en` to the `ingredients` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_fr` to the `ingredients` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_en` to the `units` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_fr` to the `units` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "ingredients_name_key";

-- DropIndex
DROP INDEX "units_name_key";

-- AlterTable
ALTER TABLE "ingredients" DROP COLUMN "name",
ADD COLUMN     "name_en" TEXT NOT NULL,
ADD COLUMN     "name_fr" TEXT NOT NULL,
ADD COLUMN     "type_en" TEXT,
ADD COLUMN     "type_fr" TEXT;

-- AlterTable
ALTER TABLE "units" DROP COLUMN "name",
ADD COLUMN     "name_en" TEXT NOT NULL,
ADD COLUMN     "name_fr" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ingredients_name_en_key" ON "ingredients"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "ingredients_name_fr_key" ON "ingredients"("name_fr");

-- CreateIndex
CREATE UNIQUE INDEX "units_name_en_key" ON "units"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "units_name_fr_key" ON "units"("name_fr");
