import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateGeneralSettingDto {
  @ApiProperty({ example: 'site_name' })
  @IsString()
  key: string;

  @ApiProperty({ example: 'My Recipe Site' })
  @IsString()
  value: string;
}

export class UpdateGeneralSettingDto {
  @ApiProperty({ example: 'My Updated Recipe Site' })
  @IsString()
  value: string;
} 