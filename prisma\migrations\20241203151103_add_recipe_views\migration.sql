-- CreateTable
CREATE TABLE "recipe_views" (
    "id" BIGSERIAL NOT NULL,
    "userId" BIGINT NOT NULL,
    "recipeId" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "recipe_views_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "recipe_views" ADD CONSTRAINT "recipe_views_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "recipe_views" ADD CONSTRAINT "recipe_views_recipeId_fkey" FOREIGN KEY ("recipeId") REFERENCES "recipes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
