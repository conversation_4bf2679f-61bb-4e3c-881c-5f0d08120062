import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { NutritionElementsService } from './nutrition-elements.service';
import { CreateNutritionElementDto, UpdateNutritionElementDto } from './dto/nutrition-element.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('Nutrition Elements')
@Controller('nutrition-elements')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class NutritionElementsController {
  constructor(private readonly nutritionElementsService: NutritionElementsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new nutrition element (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Nutrition element created successfully' })
  create(@Body() dto: CreateNutritionElementDto) {
    return this.nutritionElementsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all nutrition elements (USER, ADMIN, SUPER_ADMIN)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted nutrition elements. If false, returns only active nutrition elements.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Return paginated nutrition elements',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/NutritionElement' }
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            hasNextPage: { type: 'boolean' },
            hasPreviousPage: { type: 'boolean' }
          }
        }
      }
    }
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: string,
  ) {
    return this.nutritionElementsService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted: onlyDeleted === 'true'
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get nutrition element by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the nutrition element' })
  findOne(@Param('id') id: string) {
    return this.nutritionElementsService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update nutrition element (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Nutrition element updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateNutritionElementDto) {
    return this.nutritionElementsService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete nutrition element (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Nutrition element deleted successfully' })
  remove(@Param('id') id: string) {
    return this.nutritionElementsService.remove(toBigInt(id));
  }
} 