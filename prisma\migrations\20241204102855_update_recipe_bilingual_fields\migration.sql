/*
  Warnings:

  - You are about to drop the column `difficultyLevel` on the `recipes` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `recipes` table. All the data in the column will be lost.
  - You are about to drop the column `shortDescription` on the `recipes` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name_en]` on the table `recipes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_fr]` on the table `recipes` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `difficultyLevel_en` to the `recipes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `difficultyLevel_fr` to the `recipes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_en` to the `recipes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_fr` to the `recipes` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "recipes_name_key";

-- AlterTable
ALTER TABLE "recipes" DROP COLUMN "difficultyLevel",
DROP COLUMN "name",
DROP COLUMN "shortDescription",
ADD COLUMN     "difficultyLevel_en" TEXT NOT NULL,
ADD COLUMN     "difficultyLevel_fr" TEXT NOT NULL,
ADD COLUMN     "name_en" TEXT NOT NULL,
ADD COLUMN     "name_fr" TEXT NOT NULL,
ADD COLUMN     "shortDescription_en" TEXT,
ADD COLUMN     "shortDescription_fr" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "recipes_name_en_key" ON "recipes"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "recipes_name_fr_key" ON "recipes"("name_fr");
