{"name": "qa_recette_back", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:seed": "ts-node prisma/seed.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@fastify/helmet": "^11.1.1", "@fastify/multipart": "^7.7.3", "@fastify/rate-limit": "^7.6.0", "@fastify/static": "^6.12.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.4.19", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.19", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.19", "@nestjs/platform-fastify": "^10.4.19", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.10.1", "@supabase/supabase-js": "^2.50.2", "@types/multer": "^1.4.13", "@types/passport-jwt": "^4.0.1", "@types/pdfkit": "^0.13.9", "axios": "^1.10.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "express-basic-auth": "^1.2.1", "fastify": "^4.29.1", "fastify-multer": "^2.0.3", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "multer": "1.4.5-lts.2", "nest-fastify-multer": "^2.0.0", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfkit": "^0.15.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.1"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.19", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^20.19.1", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.1", "jest": "^29.7.0", "prettier": "^3.6.1", "prisma": "^6.10.1", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}