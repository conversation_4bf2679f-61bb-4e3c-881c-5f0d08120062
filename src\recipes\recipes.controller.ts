import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Res, InternalServerErrorException } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { RecipesService } from './recipes.service';
import { CreateRecipeDto, UpdateRecipeDto } from './dto/recipe.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { DifficultyLevel } from './enums/difficulty-level.enum';
import { toBigInt } from '../common/utils/bigint.util';
import { PaginationParams } from '../common/dtos/pagination-params.dto';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '@prisma/client';
import { FastifyReply } from 'fastify';
import * as path from 'path';
import * as fs from 'fs';
import { CreateReviewDto } from './dto/review.dto';

@ApiTags('Recipes')
@Controller('recipes')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class RecipesController {
  constructor(private readonly recipesService: RecipesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new recipe (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Recipe created successfully' })
  create(@Body() dto: CreateRecipeDto) {
    return this.recipesService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all recipes (USER, ADMIN, SUPER_ADMIN)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'categoryId', required: false, type: String, description: 'Comma-separated category IDs' })
  @ApiQuery({ name: 'difficultyLevel', required: false, enum: DifficultyLevel })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ name: 'diets', required: false, type: String, description: 'Comma-separated diet IDs' })
  @ApiQuery({ name: 'allergies', required: false, type: String, description: 'Comma-separated allergy IDs' })
  @ApiQuery({ name: 'cuisineId', required: false, type: String, description: 'Comma-separated cuisine IDs' })
  @ApiQuery({ name: 'onlyDeleted', required: false, type: Boolean, description: 'If true, returns only deleted recipes. If false, returns only active recipes.' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return paginated recipes',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Recipe' }
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            hasNextPage: { type: 'boolean' },
            hasPreviousPage: { type: 'boolean' }
          }
        }
      }
    }
  })
  findAll(
    @Query() paginationParams: PaginationParams,
    @Query('search') search?: string,
    @Query('categoryId') categoryId?: string,
    @Query('difficultyLevel') difficultyLevel?: DifficultyLevel,
    @Query('order') order?: 'asc' | 'desc',
    @Query('diets') diets?: string,
    @Query('allergies') allergies?: string,
    @Query('cuisineId') cuisineId?: string,
    @Query('onlyDeleted') onlyDeleted?: boolean,
    @CurrentUser() user?: User
  ) {
    return this.recipesService.findAll({
      ...paginationParams,
      search,
      categoryIds: categoryId?.split(',').filter(Boolean),
      difficultyLevel,
      order,
      diets: diets?.split(',').filter(Boolean) || [],
      allergies: allergies?.split(',').filter(Boolean) || [],
      cuisineIds: cuisineId?.split(',').filter(Boolean) || [],
      onlyDeleted
    }, user?.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get recipe by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the recipe' })
  findOne(@Param('id') id: string, @CurrentUser() user?: User) {
    return this.recipesService.findOne(toBigInt(id), user?.id);
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update recipe (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Recipe updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateRecipeDto) {
    return this.recipesService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete recipe (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Recipe deleted successfully' })
  remove(@Param('id') id: string) {
    return this.recipesService.remove(toBigInt(id));
  }

  @Get('client/latest')
  @ApiOperation({ summary: 'Get latest 10 recipes for clients' })
  @UseGuards()
  getLatestRecipes(
    @CurrentUser() user?: User
  ) {
    return this.recipesService.getLatestRecipes(user?.id);
  }

  @Post('client/like/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Like a recipe' })
  @ApiResponse({ status: 200, description: 'Recipe liked successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async likeRecipe(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.recipesService.likeRecipe(toBigInt(id), user.id);
  }

  @Delete('client/like/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Unlike a recipe' })
  @ApiResponse({ status: 200, description: 'Recipe unliked successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async unlikeRecipe(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.recipesService.unlikeRecipe(toBigInt(id), user.id);
  }

  @Post('client/wishlist/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add recipe to wishlist' })
  @ApiResponse({ status: 200, description: 'Recipe added to wishlist successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async addToWishlist(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.recipesService.addToWishlist(toBigInt(id), user.id);
  }

  @Delete('client/wishlist/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Remove recipe from wishlist' })
  @ApiResponse({ status: 200, description: 'Recipe removed from wishlist successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async removeFromWishlist(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.recipesService.removeFromWishlist(toBigInt(id), user.id);
  }

  @Get('client/wishlist')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get user wishlisted recipes' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ 
    status: 200, 
    description: 'Return paginated wishlisted recipes',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Recipe' }
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            hasNextPage: { type: 'boolean' },
            hasPreviousPage: { type: 'boolean' }
          }
        }
      }
    }
  })
  getWishlistedRecipes(
    @CurrentUser() user: User,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
  ) {
    return this.recipesService.getWishlistedRecipes(user.id, {
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order
    });
  }

  @Post('client/view/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add recipe view' })
  @ApiResponse({ status: 200, description: 'Recipe view added successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async addView(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.recipesService.addView(toBigInt(id), user.id);
  }

  @Get('client/mostPopular')
  @ApiOperation({ summary: 'Get top 10 most viewed recipes' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return most popular recipes',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Recipe' }
        }
      }
    }
  })
  getMostPopularRecipes(
    @CurrentUser() user?: User
  ) {
    return this.recipesService.getMostPopularRecipes(user?.id);
  }

  @Get('client/discover')
  @ApiOperation({ summary: 'Get random recipes' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({name:'page', required: false, type: Number})
  getRandomRecipes(
    @Query() query: { page?: number; limit?: number },
    @CurrentUser() user: User
  ) {
    return this.recipesService.getRandomRecipes({
      ...query,
      userId: user.id
    });
  }

  @Get('client/downloadpdf/:id')
  @ApiOperation({ summary: 'Download recipe as PDF' })
  @ApiResponse({ status: 200, description: 'Returns PDF file' })
  async downloadPDF(
    @Param('id') id: string,
    @Res({ passthrough: false }) res: FastifyReply
  ) {
    try {
      const { filename, filePath } = await this.recipesService.generateRecipePDF(toBigInt(id));

      const stream = fs.createReadStream(filePath);
      
      // Set response type to avoid JSON serialization
      res.type('application/pdf');
      res.header('Content-Disposition', `attachment; filename="recipe-${id}.pdf"`);
      
      // Handle stream errors
      stream.on('error', (error) => {
        console.error('Error reading PDF file:', error);
        throw new InternalServerErrorException('Error reading PDF file');
      });

      // Clean up file after response is sent
      res.raw.on('finish', () => {
        fs.unlink(filePath, (err) => {
          if (err) console.error('Error deleting temporary PDF:', err);
        });
      });

      // Send the stream
      return res.send(stream);
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new InternalServerErrorException('Error generating PDF');
    }
  }

  @Post('client/review/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add a review to a recipe' })
  @ApiResponse({ status: 201, description: 'Review added successfully' })
  @ApiResponse({ status: 404, description: 'Recipe not found' })
  async addReview(
    @Param('id') id: string,
    @Body() dto: CreateReviewDto,
    @CurrentUser() user: User
  ) {
    return this.recipesService.addReview(toBigInt(id), user.id, dto);
  }
} 