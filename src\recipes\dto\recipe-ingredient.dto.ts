import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>N<PERSON><PERSON>, Min, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { toBigInt } from '../../common/utils/bigint.util';

export class RecipeIngredientDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => value ? toBigInt(value) : undefined)
  ingredientId?: bigint;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  quantity: number;

  @ApiProperty()
  @Transform(({ value }) => toBigInt(value))
  unitId: bigint;
} 