/*
  Warnings:

  - You are about to drop the column `name` on the `nutrition_elements` table. All the data in the column will be lost.
  - You are about to drop the column `content` on the `recipe_steps` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name_en]` on the table `nutrition_elements` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_fr]` on the table `nutrition_elements` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `name_en` to the `nutrition_elements` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_fr` to the `nutrition_elements` table without a default value. This is not possible if the table is not empty.
  - Added the required column `content_en` to the `recipe_steps` table without a default value. This is not possible if the table is not empty.
  - Added the required column `content_fr` to the `recipe_steps` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "nutrition_elements_name_key";

-- AlterTable
ALTER TABLE "nutrition_elements" DROP COLUMN "name",
ADD COLUMN     "name_en" TEXT NOT NULL,
ADD COLUMN     "name_fr" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "recipe_steps" DROP COLUMN "content",
ADD COLUMN     "content_en" TEXT NOT NULL,
ADD COLUMN     "content_fr" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "nutrition_elements_name_en_key" ON "nutrition_elements"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "nutrition_elements_name_fr_key" ON "nutrition_elements"("name_fr");
