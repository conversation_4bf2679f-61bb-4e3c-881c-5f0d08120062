import { PrismaClient } from '@prisma/client';

export async function seedRegions(prisma: PrismaClient) {
  const regions = [
    {
      name: 'ABITIBI',
    },
    {
      name: 'Gatineau',
    },
    {
      name: 'LA PRAIRIE	',
    },
    {
      name: 'LAURENTIDES	',
    },
    {
      name: '<PERSON><PERSON> QUARTIER	',
    },
    {
      name: '<PERSON>ON<PERSON>ÉAL	',
    },
    {
      name: 'Ottawa	',
    },
    {
      name: 'QUÉBEC	',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>	',
    },
    {
      name: 'Saguenay	',
    },
    {
      name: 'SHERBROOKE	',
    },
    {
      name: 'TERREBONNE	',
    },
    {
      name: 'TROIS-RIVIÈRES	',
    }
  ];

  for (const r of regions) {
    await prisma.region.create({
      data: r
    });
  }

  console.log('Regions seeded successfully');
} 