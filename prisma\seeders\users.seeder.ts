// import { PrismaClient } from '@prisma/client';
// import * as bcrypt from 'bcrypt';

// export async function seedUsers(prisma: PrismaClient) {
//   const users = [
//     {
//       email: '<EMAIL>',
//       password: 'password123',
//       fullName: 'Regular User',
//       status: 'ACTIVE',
//       role: 'USER'
//     },
//     {
//       email: '<EMAIL>',
//       password: 'password123',
//       fullName: 'Admin User',
//       status: 'ACTIVE',
//       role: 'ADMIN'
//     },
//     {
//       email: '<EMAIL>',
//       password: 'password123',
//       fullName: 'Super Admin',
//       status: 'ACTIVE',
//       role: 'SUPER_ADMIN'
//     }
//   ];

//   for (const user of users) {
//     const hashedPassword = await bcrypt.hash(user.password, 10);

//     await prisma.user.upsert({
//       where: { email: user.email },
//       update: {},
//       create: {
//         email: user.email,
//         password: hashedPassword,
//         fullName: user.fullName,
//         status: user.status,
//         emailVerifiedAt: new Date(),
//         roles: {
//           create: {
//             role: {
//               connect: {
//                 name: user.role
//               }
//             }
//           }
//         }
//       }
//     });
//   }

//   console.log('Users seeded successfully');
// } 