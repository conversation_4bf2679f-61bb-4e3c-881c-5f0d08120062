import { Module } from '@nestjs/common';
import { NotificationsController } from './notifications.controller';
import { FirebaseService } from '../common/services/firebase.service';
import { PrismaService } from '../prisma/prisma.service';

@Module({
  controllers: [NotificationsController],
  providers: [FirebaseService, PrismaService],
  exports: [FirebaseService]
})
export class NotificationsModule {} 