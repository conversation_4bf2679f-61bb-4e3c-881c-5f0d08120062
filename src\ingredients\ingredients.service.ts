import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateIngredientDto, UpdateIngredientDto } from './dto/ingredient.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class IngredientsService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateIngredientDto) {
    const exists = await this.prisma.ingredient.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en
          ? `Ingredient with English name "${dto.name_en}" already exists`
          : `Ingredient with French name "${dto.name_fr}" already exists`
      );
    }

    return this.prisma.ingredient.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.IngredientWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } },
          { type_en: { contains: search, mode: 'insensitive' } },
          { type_fr: { contains: search, mode: 'insensitive' } },
          { description_en: { contains: search, mode: 'insensitive' } },
          { description_fr: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.ingredient.count({ where });

    const ingredients = await this.prisma.ingredient.findMany({
      where,
      orderBy: { createdAt: order },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      items: ingredients,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const ingredient = await this.prisma.ingredient.findFirst({
      where: { 
        id,
        deletedAt: null 
      }
    });

    if (!ingredient) {
      throw new NotFoundException(`Ingredient with ID ${id} not found`);
    }

    return ingredient;
  }

  async update(id: bigint, dto: UpdateIngredientDto) {
    const ingredient = await this.prisma.ingredient.findFirst({
      where: { id }
    });

    if (!ingredient) {
      throw new NotFoundException(`Ingredient with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr) {
      const exists = await this.prisma.ingredient.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en
            ? `Ingredient with English name "${dto.name_en}" already exists`
            : `Ingredient with French name "${dto.name_fr}" already exists`
        );
      }
    }

    return this.prisma.ingredient.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.ingredient.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }
} 