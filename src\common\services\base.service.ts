import { PaginatedResponse } from '../dto/response.dto';
import { BaseQueryDto } from '../dto/query.dto';

export abstract class BaseService {
  protected getPaginationData<T>(
    items: T[],
    total: number,
    query: BaseQueryDto
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(total / query.limit);
    
    return {
      items,
      page: query.page,
      limit: query.limit,
      total,
      totalPages,
    };
  }
} 