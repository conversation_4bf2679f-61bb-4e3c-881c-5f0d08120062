import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCuisineDto } from './dto/cuisine.dto';
import { UpdateCuisineDto } from './dto/cuisine.dto';
import { CuisineQueryDto } from './dto/cuisine.dto';
import { Prisma } from '@prisma/client';
import { UploadService } from '../common/services/upload.service';

@Injectable()
export class CuisinesService {
  constructor(
    private prisma: PrismaService,
    private uploadService: UploadService
  ) {}

  async create(dto: CreateCuisineDto) {
    let imagePath: string | undefined;
    
    if (dto.image) {
      imagePath = await this.uploadService.uploadImage(dto.image, 'cuisines');
    }

    return this.prisma.cuisine.create({
      data: {
        name_en: dto.name_en,
        name_fr: dto.name_fr,
        image: imagePath,
      },
    });
  }

  async findAll(query: CuisineQueryDto) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const skip = (page - 1) * limit;

    const where: Prisma.CuisineWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
          { name_fr: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
        ]
      })
    };

    const total = await this.prisma.cuisine.count({ where });

    const items = await this.prisma.cuisine.findMany({
      where,
      orderBy: { id: order },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      items,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const cuisine = await this.prisma.cuisine.findFirst({
      where: { id, deletedAt: null },
    });

    if (!cuisine) {
      throw new NotFoundException(`Cuisine with ID ${id} not found`);
    }

    return cuisine;
  }

  async update(id: bigint, dto: UpdateCuisineDto) {
    const cuisine = await this.prisma.cuisine.findUnique({
      where: { id },
    });

    if (!cuisine) {
      throw new NotFoundException(`Cuisine with ID ${id} not found`);
    }

    let imagePath: string | undefined;
    
    if (dto.image) {
      imagePath = await this.uploadService.uploadImage(dto.image, 'cuisines');
    }

    return this.prisma.cuisine.update({
      where: { id },
      data: {
        name_en: dto.name_en,
        name_fr: dto.name_fr,
        deletedAt: dto.deletedAt,
        ...(imagePath && { image: imagePath }),
      },
    });
  }

  async remove(id: bigint) {
    const cuisine = await this.prisma.cuisine.findUnique({
      where: { id },
    });

    if (!cuisine) {
      throw new NotFoundException(`Cuisine with ID ${id} not found`);
    }

    return this.prisma.cuisine.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
} 