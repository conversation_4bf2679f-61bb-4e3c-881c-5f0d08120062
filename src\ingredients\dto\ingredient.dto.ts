import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateIngredientDto {
  @ApiProperty({ example: 'Tomato' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Tomate' })
  @IsString()
  name_fr: string;

  @ApiProperty({ example: 'Vegetable', required: false })
  @IsOptional()
  @IsString()
  type_en?: string;

  @ApiProperty({ example: 'Légume', required: false })
  @IsOptional()
  @IsString()
  type_fr?: string;

  @ApiProperty({ example: 'Fresh red tomatoes', required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ example: 'Tomates rouges fraîches', required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;
}

export class UpdateIngredientDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  type_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  type_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;
} 