import { Injectable } from '@nestjs/common';
import { join } from 'path';
import { createWriteStream } from 'fs';
import { promisify } from 'util';
import { pipeline } from 'stream';
import { Readable } from 'stream';

const pump = promisify(pipeline);

type UploadFolder = 'users' | 'categories' | 'ingredients' | 'diets' | 'allergies' | 'recipes' | 'avatars' | 'notifications' | 'cuisines';

interface FastifyMultipartFile {
  filename: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
}

@Injectable()
export class UploadService {
  async uploadImage(
    file: FastifyMultipartFile,
    folder: UploadFolder
  ): Promise<string> {
    const fileName = this.generateFileName(file.filename);
    const filePath = join('uploads', folder, fileName);
    const writeStream = createWriteStream(join(process.cwd(), filePath));
    
    const readStream = Readable.from(file.buffer);
    await pump(readStream, writeStream);
    
    return `${folder}/${fileName}`;
  }

  private generateFileName(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    const extension = originalName.split('.').pop();
    return `${timestamp}-${random}.${extension}`;
  }
} 