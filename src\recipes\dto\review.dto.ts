import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Length } from 'class-validator';

export class CreateReviewDto {
  @ApiProperty({ 
    example: 'Great Recipe!',
    description: 'Title of the review'
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  review_title: string;

  @ApiProperty({ 
    example: 'This recipe was amazing! The instructions were clear and the result was delicious.',
    description: 'Body of the review'
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  @MaxLength(1000)
  review_body: string;
} 