-- Create<PERSON><PERSON>
CREATE TYPE "ReviewStatus" AS ENUM ('OPEN', 'IN_REVIEW', 'SOLVED');

-- CreateTable
CREATE TABLE "recipe_reviews" (
    "id" BIGSERIAL NOT NULL,
    "userId" BIGINT NOT NULL,
    "recipeId" BIGINT NOT NULL,
    "review_title" TEXT NOT NULL,
    "review_body" TEXT NOT NULL,
    "status" "ReviewStatus" NOT NULL DEFAULT 'OPEN',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "recipe_reviews_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "recipe_reviews" ADD CONSTRAINT "recipe_reviews_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "recipe_reviews" ADD CONSTRAINT "recipe_reviews_recipeId_fkey" FOREIGN KEY ("recipeId") REFERENCES "recipes"("id") ON DELETE RESTRICT ON UPDATE CASCADE; 