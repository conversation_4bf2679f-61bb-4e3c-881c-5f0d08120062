import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { toBigInt } from '../../common/utils/bigint.util';

export class RecipeNutritionDto {
  @ApiProperty({ example: 1 })
  @Transform(({ value }) => toBigInt(value))
  nutritionElementId: bigint;

  @ApiProperty({ example: 10.5 })
  @IsNumber()
  @Min(0)
  value: number;
} 