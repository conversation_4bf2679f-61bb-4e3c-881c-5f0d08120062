import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateNutritionElementDto {
  @ApiProperty({ example: 'Protein' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Protéine' })
  @IsString()
  name_fr: string;

  @ApiProperty({ example: 'g' })
  @IsString()
  unit: string;
}

export class UpdateNutritionElementDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ example: 'Protein', required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ example: 'Protéine', required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ example: 'g', required: false })
  @IsOptional()
  @IsString()
  unit?: string;
} 