import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { toBigInt } from '../../common/utils/bigint.util';

export class ResetUserPasswordDto {
  @ApiProperty({
    example: '1',
    description: 'ID of the user whose password needs to be reset',
    type: String,
    required: true
  })
  @IsNotEmpty({ message: 'User ID is required' })
  @Transform(({ value }) => toBigInt(value))
  userId: bigint;
} 