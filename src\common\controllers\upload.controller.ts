import { 
  Controller, 
  Post, 
  UseGuards,
  Param,
  BadRequestException,
  Req,
  Delete
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiConsumes, 
  ApiOperation, 
  ApiBearerAuth,
  ApiBody,
  ApiParam 
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../auth/enums/roles.enum';
import * as path from 'path';
import * as fs from 'fs';
import { FastifyRequest } from 'fastify';
import { pipeline } from 'stream/promises';

const VALID_FOLDERS = ['users', 'recipes', 'ingredients', 'categories', 'allergies', 'diets', 'notifications'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const VALID_MIME_TYPES = ['image/jpeg', 'image/jpg', 'image/png'];

@ApiTags('Upload')
@Controller('upload')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class UploadController {
  @Post(':folder')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Upload image file' })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'folder',
    enum: VALID_FOLDERS,
    description: 'Target folder for the upload'
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Image file (max 5MB, formats: jpg, jpeg, png)'
        }
      }
    }
  })
  async uploadFile(
    @Param('folder') folder: string,
    @Req() request: FastifyRequest
  ) {
    console.log('folder', folder);
    if (!VALID_FOLDERS.includes(folder)) {
      throw new BadRequestException('Invalid folder');
    }

    const data = await request.file();
    
    if (!data) {
      throw new BadRequestException('No file uploaded');
    }

    if (!VALID_MIME_TYPES.includes(data.mimetype)) {
      throw new BadRequestException('Invalid file type. Only JPG, JPEG and PNG are allowed');
    }

    const uploadDir = path.join(process.cwd(), 'uploads', folder);
    fs.mkdirSync(uploadDir, { recursive: true });

    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const filename = `${uniqueSuffix}${path.extname(data.filename)}`;
    const writePath = path.join(uploadDir, filename);

    try {
      await pipeline(data.file, fs.createWriteStream(writePath));

      return {
        filename,
        path: `${folder}/${filename}`
      };
    } catch (error) {
      throw new BadRequestException('Failed to save file' + error.message);
    }
  }

  @Delete(':filepath')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete image file' })
  async deleteFile(
    @Param('filepath') filepath: string
  ) {

    const deletePath = path.join(process.cwd(), 'uploads', filepath);
    try {
      fs.unlinkSync(deletePath);
      return {
        message: 'File deleted successfully'
      };
    } catch (error) {
      throw new BadRequestException('Failed to delete file');
    }
  }
} 