import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAllergyDto, UpdateAllergyDto } from './dto/allergy.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class AllergiesService {
  constructor(private prisma: PrismaService) {}

  private async validateIngredients(ingredientIds: bigint[]) {
    if (!ingredientIds?.length) return;

    const ingredients = await this.prisma.ingredient.findMany({
      where: {
        id: { in: ingredientIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const foundIds = ingredients.map(i => i.id);
    const invalidIds = ingredientIds.filter(id => !foundIds.includes(id));

    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid ingredient IDs: ${invalidIds.join(', ')}`);
    }
  }

  async create(dto: CreateAllergyDto) {
    const exists = await this.prisma.allergy.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en
          ? `Allergy with English name "${dto.name_en}" already exists`
          : `Allergy with French name "${dto.name_fr}" already exists`
      );
    }

    return this.prisma.allergy.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.AllergyWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } },
          { description_en: { contains: search, mode: 'insensitive' } },
          { description_fr: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.allergy.count({ where });

    const allergies = await this.prisma.allergy.findMany({
      where,
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        description_en: true,
        description_fr: true,
        image: true,
        bannedIngredients: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true
      },
      orderBy: { createdAt: order },
      skip: (page - 1) * limit,
      take: limit
    });

    const allergiesWithIngredients = await Promise.all(
      allergies.map(async (allergy) => {
        if (allergy.bannedIngredients?.length) {
          const ingredients = await this.prisma.ingredient.findMany({
            where: {
              id: { in: allergy.bannedIngredients },
              deletedAt: null
            },
            select: {
              id: true,
              name_en: true,
              name_fr: true
            }
          });
          return { ...allergy, bannedIngredients: ingredients };
        }
        return allergy;
      })
    );

    return {
      items: allergiesWithIngredients,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const allergy = await this.prisma.allergy.findFirst({
      where: { 
        id,
        deletedAt: null 
      },
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        description_en: true,
        description_fr: true,
        image: true,
        bannedIngredients: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true
      }
    });

    if (!allergy) {
      throw new NotFoundException(`Allergy with ID ${id} not found`);
    }

    if (allergy.bannedIngredients?.length) {
      const ingredients = await this.prisma.ingredient.findMany({
        where: {
          id: { in: allergy.bannedIngredients },
          deletedAt: null
        },
        select: {
          id: true,
          name_en: true,
          name_fr: true
        }
      });
      allergy.bannedIngredients = ingredients as any;
    }

    return allergy;
  }

  async update(id: bigint, dto: UpdateAllergyDto) {
    const allergy = await this.prisma.allergy.findFirst({
      where: { id }
    });

    if (!allergy) {
      throw new NotFoundException(`Allergy with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr) {
      const exists = await this.prisma.allergy.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en
            ? `Allergy with English name "${dto.name_en}" already exists`
            : `Allergy with French name "${dto.name_fr}" already exists`
        );
      }
    }

    return this.prisma.allergy.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.allergy.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }
} 