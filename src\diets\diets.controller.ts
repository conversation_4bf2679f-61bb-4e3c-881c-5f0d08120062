import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { DietsService } from './diets.service';
import { CreateDietDto, UpdateDietDto } from './dto/diet.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('Diets')
@Controller('diets')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class DietsController {
  constructor(private readonly dietsService: DietsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new diet (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Diet created successfully' })
  create(@Body() dto: CreateDietDto) {
    return this.dietsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all diets (USER, ADMIN, SUPER_ADMIN)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted diets. If false, returns only active diets.'
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: string,
  ) {
    return this.dietsService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted: onlyDeleted === 'true'
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get diet by id' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.dietsService.findOne(BigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update diet (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Diet updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateDietDto) {
    return this.dietsService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete diet (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Diet deleted successfully' })
  remove(@Param('id') id: string) {
    return this.dietsService.remove(toBigInt(id));
  }
} 