import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, IsOptional, IsEnum, IsArray, IsBoolean, IsDate } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { BaseQueryDto } from '../../common/dto/query.dto';
import { toBigInt } from '../../common/utils/bigint.util';
import { UserStatus } from '../../auth/enums/user-status.enum';
import { Role } from '../../auth/enums/roles.enum';
import { Prisma } from '@prisma/client';

export class CreateUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ example: '<PERSON>', required: false })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ 
    example: 'ACTIVE',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    required: false
  })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;

  @ApiProperty({ 
    example: ['USER'],
    description: 'Array of role names',
    required: false,
    enum: Role,
    isArray: true,
    default: ['USER']
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Role, { each: true })
  roles?: Role[] = [Role.USER];
}

export class UpdateUserDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ example: 'John Doe', required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ 
    example: 'ACTIVE',
    enum: UserStatus,
    required: false
  })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;

  @ApiProperty({ 
    example: ['USER', 'ADMIN'],
    description: 'Array of role names',
    required: false,
    enum: Role,
    isArray: true
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Role, { each: true })
  roles?: Role[];

  @ApiProperty({ example: '1234567890', required: false })
  @IsOptional()
  @Transform(({ value }) => value ? toBigInt(value) : undefined)
  id?: bigint;
}

export class UserQueryDto extends BaseQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    required: false,
    description: 'Filter by user role',
    enumName: 'Role',
    enum: [Role.USER, Role.ADMIN]
  })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiProperty({ 
    required: false,
    enum: UserStatus,
    description: 'Filter by user status'
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiProperty({ 
    required: false,
    type: Boolean,
    description: 'Include deleted records',
    default: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  includeDeleted?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({ 
    required: false,
    enum: ['asc', 'desc'],
    default: 'desc'
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order?: Prisma.SortOrder = 'desc';
} 