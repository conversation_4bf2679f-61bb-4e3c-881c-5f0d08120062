import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, Min } from 'class-validator';

export class RecipeStepDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  @Min(1)
  order: number;

  @ApiProperty({ example: 'Chop the tomatoes' })
  @IsString()
  content_en: string;

  @ApiProperty({ example: 'Couper les tomates' })
  @IsString()
  content_fr: string;
} 