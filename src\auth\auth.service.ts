import { Injectable, UnauthorizedException, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { RegisterDto, LoginDto } from './dto/auth.dto';
import * as bcrypt from 'bcrypt';
import { ConfigService } from '@nestjs/config';
import { toBigInt } from '../common/utils/bigint.util';
import { UserStatus } from './enums/user-status.enum';
import { MailerService } from '../common/services/mailer.service';
import * as crypto from 'crypto';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailerService: MailerService,
  ) {}

  async register(dto: RegisterDto) {
    try {
      // Check if user exists by email
      const existsByEmail = await this.prisma.user.findUnique({
        where: { email: dto.email }
      });

      if (existsByEmail) {
        throw new ConflictException('Email already registered');
      }

      // Check if phone number exists (if provided)
      if (dto.phoneNumber) {
        const existsByPhone = await this.prisma.user.findUnique({
          where: { phoneNumber: dto.phoneNumber }
        });

        if (existsByPhone) {
          throw new ConflictException('Phone number already registered');
        }
      }

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');

      // Hash password
      const hashedPassword = await bcrypt.hash(dto.password, 10);

      // Create user with PENDING status and verification token
      const user = await this.prisma.user.create({
        data: {
          email: dto.email,
          password: hashedPassword,
          fullName: dto.fullName,
          phoneNumber: dto.phoneNumber,
          status: UserStatus.ACTIVE,
          roles: {
            create: {
              role: {
                connect: {
                  name: 'USER'
                }
              }
            }
          },
          verificationToken: {
            create: {
              token: verificationToken,
              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
            }
          },
          diets: dto.diets?.length ? {
            create: dto.diets.map(dietId => ({
              diet: {
                connect: { id: BigInt(dietId) }
              }
            }))
          } : undefined,
          allergies: dto.allergies?.length ? {
            create: dto.allergies.map(allergyId => ({
              allergy: {
                connect: { id: BigInt(allergyId) }
              }
            }))
          } : undefined,
        },
        include: {
          roles: {
            include: {
              role: {
                include: {
                  permissions: true
                }
              }
            }
          },
          verificationToken: true,
          diets: {
            include: {
              diet: true
            }
          },
          allergies: {
            include: {
              allergy: true
            }
          }
        }
      });

      // Send verification email
      await this.mailerService.sendVerificationEmail(
        user.email,
        verificationToken
      );
      
      return {
        message: 'Registration successful. Please check your email to verify your account.',
        user: this.excludePassword(user)
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      
      throw new Error(`Failed to register user: ${error.message}`);
    }
  }

  private async generateAccessToken(user: any) {
    return this.jwtService.signAsync(
      { 
        sub: user.id.toString(),
        email: user.email,
        roles: user.roles.map(r => r.role.name)
      },
      {
        expiresIn: '1d',
        secret: this.configService.get<string>('JWT_SECRET')
      }
    );
  }

  private async generateRefreshToken(user: any) {
    const token = await this.jwtService.signAsync(
      { sub: user.id.toString() },
      {
        expiresIn: '30d',
        secret: this.configService.get<string>('JWT_REFRESH_SECRET')
      }
    );

    // Store refresh token in database
    await this.prisma.refreshToken.create({
      data: {
        token,
        userId: BigInt(user.id),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      }
    });

    return token;
  }

  async login(dto: LoginDto) {
    // Find user and check if not deleted
    const user = await this.prisma.user.findFirst({
      where: { 
        email: dto.email,
        deletedAt: null
      },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: true
              }
            }
          }
        },
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(dto.password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate tokens
    const [accessToken, refreshToken] = await Promise.all([
      this.generateAccessToken(user),
      this.generateRefreshToken(user)
    ]);

    // Get all unique permissions (from roles and direct assignments)
    const rolePermissions = user.roles.flatMap(r => r.role.permissions);
    const directPermissions = user.permissions.map(up => up.permission);
    
    // Combine and deduplicate permissions
    const allPermissions = [...rolePermissions, ...directPermissions]
      .filter((permission, index, self) => 
        index === self.findIndex(p => p.id === permission.id)
      )
      .map(p => p.name);

    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        roles: user.roles.map(r => ({
          roleId: r.role.id.toString(),
          assignedAt: r.assignedAt,
          name: r.role.name
        })),
        permissions: allPermissions
      }
    };
  }

  async generateTokens(user: any) {
    const [accessToken, refreshToken] = await Promise.all([
      this.generateAccessToken(user),
      this.generateRefreshToken(user)
    ]);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 3600,
      refresh_token_expires_in: 604800
    };
  }

  private excludePassword(user: any) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify token
      const { sub } = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET')
      });

      // Check if token exists and is not revoked
      const storedToken = await this.prisma.refreshToken.findFirst({
        where: {
          token: refreshToken,
          revokedAt: null,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      if (!storedToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Get user
      const user = await this.prisma.user.findUnique({
        where: { id: toBigInt(sub) },
        include: {
          roles: {
            include: {
              role: {
                include: {
                  permissions: true
                }
              }
            }
          }
        }
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Revoke old refresh token
      await this.prisma.refreshToken.update({
        where: { id: storedToken.id },
        data: { revokedAt: new Date() }
      });

      // Generate new tokens
      return this.generateTokens(user);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async revokeAllUserTokens(userId: bigint) {
    await this.prisma.refreshToken.updateMany({
      where: {
        userId,
        revokedAt: null
      },
      data: {
        revokedAt: new Date()
      }
    });
  }

  async getProfile(userId: string) {
    const user = await this.prisma.user.findFirst({
      where: { 
        id: toBigInt(userId),
        deletedAt: null
      },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return this.excludePassword(user);
  }

  async findUserById(userId: string | number | bigint) {
    return this.prisma.user.findUnique({
      where: { id: toBigInt(userId) },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: true
              }
            }
          }
        }
      }
    });
  }

  async updateAvatar(userId: string, avatarUrl: string) {
    return this.prisma.user.update({
      where: { id: toBigInt(userId) },
      data: { avatar: avatarUrl }
    });
  }

  async verifyEmail(token: string) {
    const verificationToken = await this.prisma.verificationToken.findUnique({
      where: { token },
      include: { user: true }
    });

    if (!verificationToken) {
      throw new UnauthorizedException('Invalid verification token');
    }

    if (verificationToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Verification token has expired');
    }

    await this.prisma.user.update({
      where: { id: verificationToken.userId },
      data: { 
        status: UserStatus.ACTIVE,
        emailVerifiedAt: new Date()
      }
    });

    await this.prisma.verificationToken.delete({
      where: { id: verificationToken.id }
    });

    return { message: 'Email verified successfully' };
  }

  async forgotPassword(email: string) {
    const user = await this.prisma.user.findUnique({
      where: { email }
    });

    // Always return success even if email doesn't exist (security best practice)
    if (!user) {
      return {
        message: 'If an account exists with this email, you will receive a password reset link.'
      };
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString('hex');

    // Save reset token
    await this.prisma.passwordResetToken.create({
      data: {
        token,
        userId: user.id,
        expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
      }
    });

    // Send reset email
    await this.mailerService.sendPasswordResetEmail(user.email, token);

    return {
      message: 'If an account exists with this email, you will receive a password reset link.'
    };
  }

  async resetPassword(token: string, newPassword: string) {
    const resetToken = await this.prisma.passwordResetToken.findUnique({
      where: { token },
      include: { user: true }
    });

    if (!resetToken || resetToken.usedAt || resetToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Invalid or expired reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and mark token as used
    await this.prisma.$transaction([
      this.prisma.user.update({
        where: { id: resetToken.userId },
        data: { password: hashedPassword }
      }),
      this.prisma.passwordResetToken.update({
        where: { id: resetToken.id },
        data: { usedAt: new Date() }
      })
    ]);

    // Revoke all refresh tokens for security
    await this.revokeAllUserTokens(resetToken.userId);

    return { message: 'Password reset successful. Please login with your new password.' };
  }

  async getProfileWithPreferences(userId: bigint) {
    const user = await this.prisma.user.findFirst({
      where: { 
        id: userId,
        deletedAt: null
      },
      include: {
        diets: {
          include: {
            diet: true
          }
        },
        allergies: {
          include: {
            allergy: true
          }
        }
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get all active diets and allergies
    const [allDiets, allAllergies] = await Promise.all([
      this.prisma.diet.findMany({
        where: { 
          deletedAt: null  // Changed from isActive: true
        }
      }),
      this.prisma.allergy.findMany({
        where: { 
          deletedAt: null  // Changed from isActive: true
        }
      })
    ]);

    // Map diets with status
    const dietsWithStatus = allDiets.map(diet => ({
      ...diet,
      status: user.diets.some(ud => ud.dietId === diet.id)
    }));

    // Map allergies with status
    const allergiesWithStatus = allAllergies.map(allergy => ({
      ...allergy,
      status: user.allergies.some(ua => ua.allergyId === allergy.id)
    }));

    return {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      phoneNumber: user.phoneNumber,
      avatar: user.avatar,
      status: user.status,
      diets: dietsWithStatus,
      allergies: allergiesWithStatus
    };
  }

  async updateProfile(userId: bigint, dto: UpdateProfileDto) {
    const user = await this.prisma.user.update({
      where: { id: userId },
      data: {
        fullName: dto.fullName,
        phoneNumber: dto.phoneNumber,
        fcmToken: dto.fcm_token,
        diets: {
          deleteMany: {},
          create: dto.diets?.map(dietId => ({
            dietId: toBigInt(dietId)
          })) || []
        },
        allergies: {
          deleteMany: {},
          create: dto.allergies?.map(allergyId => ({
            allergyId: toBigInt(allergyId)
          })) || []
        }
      },
      include: {
        diets: {
          include: {
            diet: true
          }
        },
        allergies: {
          include: {
            allergy: true
          }
        }
      }
    });

    return user;
  }

  // Add a method for soft delete
  async deleteAccount(userId: bigint) {
    try {
      await this.prisma.$transaction(async (tx) => {
        // Soft delete the user
        await tx.user.update({
          where: { id: userId },
          data: { 
            deletedAt: new Date(),
            // Optionally nullify unique fields to allow reuse
            email: `deleted_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`,
            phoneNumber: null
          }
        });

        // Revoke all refresh tokens
        await tx.refreshToken.updateMany({
          where: { 
            userId,
            revokedAt: null
          },
          data: { revokedAt: new Date() }
        });
      });

      return { message: 'Account deleted successfully' };
    } catch (error) {
      throw new BadRequestException('Failed to delete account');
    }
  }
} 