import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray, IsN<PERSON>ber, Matches } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ 
    required: false,
    example: '+1234567890'
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format (e.g., +1234567890)'
  })
  phoneNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fcm_token?: string;

  @ApiProperty({ 
    required: false,
    type: [Number],
    example: [1, 2]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  diets?: number[];

  @ApiProperty({ 
    required: false,
    type: [Number],
    example: [1, 2]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  allergies?: number[];
} 