import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, IsBoolean, IsOptional, IsUrl } from 'class-validator';

export class SendNotificationDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty({ required: false, description: 'Public URL of the image' })
  @IsOptional()
  @IsUrl()
  image?: string;

  @ApiProperty({ type: [Number], required: false })
  @IsOptional()
  @IsArray()
  users?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  all?: boolean;
} 