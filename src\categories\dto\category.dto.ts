import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCategoryDto {
  @ApiProperty({ example: 'Main Course' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Plat Principal' })
  @IsString()
  name_fr: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;
}

export class UpdateCategoryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ example: 'Main Course', required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ example: 'Plat Principal', required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;
} 