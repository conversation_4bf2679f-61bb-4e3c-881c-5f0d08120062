import { Injectable, UnauthorizedException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TwilioService } from '../common/services/twilio.service';
import { ExternalApiService } from '../common/services/external-api.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import {
  CheckPhoneDto,
  PhonePasswordLoginDto,
  RequestOtpDto,
  VerifyOtpDto,
  SetPasswordDto,
  CheckPhoneResponseDto,
  ClientLoginDto
} from './dto/client-auth.dto';
import { UserStatus } from './enums/user-status.enum';
import { Role } from './enums/roles.enum';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class ClientAuthService {
  constructor(
    private prisma: PrismaService,
    private twilioService: TwilioService,
    private externalApiService: ExternalApiService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  private generateOTP(): string {
    //return Math.floor(100000 + Math.random() * 900000).toString();
    return '123456';
  }

  private async generateToken(user: any): Promise<string> {
    return this.jwtService.signAsync(
      {
        sub: user.id.toString(),
        phone: user.phoneNumber,
        roles: user.roles.map((r: any) => r.role.name)
      },
      {
        expiresIn: '30d',
        secret: this.configService.get<string>('JWT_SECRET')
      }
    );
  }

  private sanitizeUser(user: any) {
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  // 1. Check if user exists by phone number
  async checkPhone(dto: CheckPhoneDto): Promise<CheckPhoneResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { phoneNumber: dto.phone }
    });

    return {
      userExists: !!user,
      message: user ? 'User found' : 'User not found'
    };
  }

  // 2. Login with phone and password (for existing users)
  async loginWithPhonePassword(dto: PhonePasswordLoginDto) {
    const user = await this.prisma.user.findUnique({
      where: { phoneNumber: dto.phone },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user has a real password (not temporary)
    if (user.status === UserStatus.NEW) {
      throw new UnauthorizedException('Please complete your registration first');
    }

    const isPasswordValid = await bcrypt.compare(dto.password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid password');
    }

    const token = await this.generateToken(user);

    return {
      user: this.sanitizeUser(user),
      access_token: token
    };
  }

  // 3. Request OTP for new users
  async requestOtp(dto: RequestOtpDto) {
    const otp = this.generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    try {
      // Find existing OTP record or create new one
      await this.prisma.userOtp.upsert({
        where: {
          phone: dto.phone
        },
        update: {
          otp,
          expiresAt,
          verified: false,
          attempts: 0
        },
        create: {
          phone: dto.phone,
          otp,
          expiresAt
        }
      });

      // await this.twilioService.sendSMS(
      //   dto.phone,
      //   dto.locale === 'fr' ?
      //   `Votre code de vérification est: ${otp}. Valide pour 10 minutes.` :
      //   `Your verification code is: ${otp}. Valid for 10 minutes.`
      // );

      return { message: 'OTP sent successfully' };
    } catch (error) {
      await this.prisma.userOtp.deleteMany({
        where: { phone: dto.phone, otp }
      });
      throw new BadRequestException('Failed to send OTP');
    }
  }

  // Legacy method for backward compatibility
  async login(dto: ClientLoginDto) {
    return this.requestOtp(dto);
  }

  // 4. Verify OTP (for new users only - just validates OTP and returns verification token)
  async verifyOtp(dto: VerifyOtpDto) {
    const otpRecord = await this.prisma.userOtp.findFirst({
      where: {
        phone: dto.phone,
        verified: false,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (otpRecord.expiresAt < new Date()) {
      throw new UnauthorizedException('OTP has expired');
    }

    if (otpRecord.attempts >= 3) {
      throw new UnauthorizedException('Too many attempts. Please request a new OTP');
    }

    if (otpRecord.otp !== dto.otp) {
      await this.prisma.userOtp.update({
        where: { id: otpRecord.id },
        data: { attempts: { increment: 1 } }
      });
      throw new UnauthorizedException('Invalid OTP');
    }

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { phoneNumber: dto.phone }
    });

    if (existingUser) {
      throw new BadRequestException('User already exists. Please use login with password.');
    }

    // Mark OTP as verified and generate a temporary verification token
    await this.prisma.userOtp.update({
      where: { id: otpRecord.id },
      data: { verified: true }
    });

    // Generate a signed verification token that expires in 30 minutes
    const verificationToken = await this.jwtService.signAsync(
      {
        phone: dto.phone,
        otpId: otpRecord.id.toString(),
        purpose: 'set_password'
      },
      {
        expiresIn: '30m',
        secret: this.configService.get<string>('JWT_SECRET')
      }
    );

    return {
      message: 'OTP verified successfully. Please set your password.',
      verificationToken,
      expiresIn: 1800 // 30 minutes in seconds
    };
  }

  // 5. Set password for new users after OTP verification
  async setPassword(dto: SetPasswordDto) {
    // Verify the verification token
    let tokenPayload: any;
    try {
      tokenPayload = await this.jwtService.verifyAsync(dto.verificationToken, {
        secret: this.configService.get<string>('JWT_SECRET')
      });
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired verification token');
    }

    // Validate token purpose and extract phone
    if (tokenPayload.purpose !== 'set_password') {
      throw new UnauthorizedException('Invalid verification token');
    }

    const phone = tokenPayload.phone;
    const otpId = tokenPayload.otpId;

    // Verify the OTP record still exists and is verified
    const otpRecord = await this.prisma.userOtp.findFirst({
      where: {
        id: BigInt(otpId),
        phone: phone,
        verified: true,
      }
    });

    if (!otpRecord) {
      throw new UnauthorizedException('Invalid verification token or OTP not found');
    }

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { phoneNumber: phone }
    });

    if (existingUser) {
      throw new BadRequestException('User already exists');
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(dto.password, 10);

    // Check external API for user data
    const externalUser = await this.externalApiService.findUserByPhone(phone);

    let userData: any;
    if (externalUser.message === 'not found') {
      // Create new user with provided data
      const randomString = crypto.randomBytes(8).toString('hex');
      userData = {
        email: `${randomString}@temp.com`,
        password: hashedPassword,
        fullName: dto.fullName || null,
        phoneNumber: phone,
        status: UserStatus.ACTIVE,
      };
    } else {
      // Create user with data from external API
      userData = {
        email: externalUser.email,
        password: hashedPassword,
        fullName: dto.fullName || externalUser.fullname,
        phoneNumber: phone,
        status: UserStatus.ACTIVE,
      };
    }

    const user = await this.prisma.user.create({
      data: {
        ...userData,
        roles: {
          create: {
            role: {
              connect: {
                name: Role.USER
              }
            }
          }
        }
      },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    // Clean up the OTP record
    await this.prisma.userOtp.delete({
      where: { id: otpRecord.id }
    });

    const token = await this.generateToken(user);

    return {
      user: this.sanitizeUser(user),
      access_token: token,
      message: 'Account created successfully'
    };
  }
} 