import { ApiProperty } from '@nestjs/swagger';

class DietStatus {
  @ApiProperty()
  id: bigint;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string | null;

  @ApiProperty()
  image: string | null;

  @ApiProperty()
  status: boolean;
}

class AllergyStatus {
  @ApiProperty()
  id: bigint;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string | null;

  @ApiProperty()
  image: string | null;

  @ApiProperty()
  status: boolean;
}

export class ProfileResponse {
  @ApiProperty()
  id: bigint;

  @ApiProperty()
  email: string;

  @ApiProperty({ required: false })
  fullName: string | null;

  @ApiProperty({ required: false })
  phoneNumber: string | null;

  @ApiProperty({ required: false })
  avatar: string | null;

  @ApiProperty()
  status: string;

  @ApiProperty({ type: [DietStatus] })
  diets: DietStatus[];

  @ApiProperty({ type: [AllergyStatus] })
  allergies: AllergyStatus[];
} 