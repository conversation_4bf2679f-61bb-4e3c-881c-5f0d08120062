import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import fastifyHelmet from '@fastify/helmet';
import fastifyStatic from '@fastify/static';
import fastifyRateLimit from '@fastify/rate-limit';
import { join } from 'path';
import { ensureUploadsDirectory } from './common/utils/ensure-uploads.util';
import { contentParser } from 'fastify-multer';
import { FastifyInstance } from 'fastify';
import { BigIntSerializationInterceptor } from './common/interceptors/bigint-serialization.interceptor';
import fastifyMultipart from '@fastify/multipart';

async function bootstrap() {
  // Ensure uploads directory exists
  ensureUploadsDirectory();

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter()
  );

  const fastifyInstance = app.getHttpAdapter().getInstance() as FastifyInstance;

  // Add Helmet security headers
  await fastifyInstance.register(fastifyHelmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: [`'self'`],
        styleSrc: [`'self'`, `'unsafe-inline'`],
        imgSrc: [`'self'`, 'data:', 'validator.swagger.io'],
        scriptSrc: [`'self'`, `https: 'unsafe-inline'`],
      },
    },
    // Allow uploads directory to be served
    crossOriginResourcePolicy: { 
      policy: 'cross-origin' 
    }
  });

  // Add rate limiting
  await fastifyInstance.register(fastifyRateLimit, {
    max: 300,
    timeWindow: '1 minute',
    errorResponseBuilder: function (request, context) {
      return {
        code: 429,
        error: 'Too Many Requests',
        message: `Too many requests, please try again later.`,
        date: Date.now(),
        expiresIn: context.ttl
      }
    }
  });

  // Register static file serving for uploads directory
  await fastifyInstance.register(fastifyStatic, {
    root: join(process.cwd(), 'uploads'),
    prefix: '/uploads/',
    decorateReply: false
  });

  await fastifyInstance.register(fastifyMultipart, {
    limits: {
      fileSize: 5 * 1024 * 1024 // 5MB
    }
  });

  // Enable CORS
  await app.enableCors();

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    })
  );

  // Add global BigInt serialization
  app.useGlobalInterceptors(new BigIntSerializationInterceptor());

  // Swagger setup (only in non-production)
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('QA Recette API')
      .setDescription('The QA Recette API Documentation')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth'
      )
      .addTag('Notifications', 'Notification management endpoints')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    
    // Add basic auth middleware for Swagger UI
    app.use('/api', (req, res, next) => {
      const auth = req.headers.authorization;
      
      if (!auth) {
        res.setHeader('WWW-Authenticate', 'Basic');
        res.statusCode = 401;
        res.end('Authentication required');
        return;
      }

      const [, credentials] = auth.split(' ');
      const [username, password] = Buffer.from(credentials, 'base64')
        .toString()
        .split(':');

      if (
        username === process.env.SWAGGER_USER && 
        password === process.env.SWAGGER_PASSWORD
      ) {
        next();
        return;
      }

      res.setHeader('WWW-Authenticate', 'Basic');
      res.statusCode = 401;
      res.end('Invalid credentials');
    });

    SwaggerModule.setup('api', app, document);
  }

  // Start the server
  await app.listen(3000, '0.0.0.0');
}

bootstrap();
