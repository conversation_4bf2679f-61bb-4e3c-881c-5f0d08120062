import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LinkDto } from './dto/link.dto';

@Injectable()
export class LinksService {
  constructor(private prisma: PrismaService) {}

  async findByRecipe(recipeId: bigint) {
    return this.prisma.link.findMany({
      where: { recipeId },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }
} 