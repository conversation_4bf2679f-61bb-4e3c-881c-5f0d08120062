import { Module } from '@nestjs/common';
import { NutritionElementsService } from './nutrition-elements.service';
import { NutritionElementsController } from './nutrition-elements.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [NutritionElementsController],
  providers: [NutritionElementsService],
  exports: [NutritionElementsService]
})
export class NutritionElementsModule {} 