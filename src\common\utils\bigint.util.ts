export function toBigInt(value: string | number | bigint | null | undefined): bigint | undefined {
  if (value === null || value === undefined || value === '') {
    return undefined;
  }

  try {
    // If it's already a bigint, return it
    if (typeof value === 'bigint') {
      return value;
    }

    // If it's a number, convert directly
    if (typeof value === 'number') {
      return BigInt(Math.floor(value));
    }

    // If it's a string, clean it first
    if (typeof value === 'string') {
      // Remove any non-numeric characters except minus sign
      const cleaned = value.replace(/[^\d-]/g, '');
      if (!cleaned) {
        return undefined;
      }
      return BigInt(cleaned);
    }

    return undefined;
  } catch (error) {
    // If conversion fails, return undefined
    return undefined;
  }
}

// Helper function to check if a value can be converted to BigInt
export function isValidBigInt(value: any): boolean {
  try {
    toBigInt(value);
    return true;
  } catch {
    return false;
  }
} 