import { PrismaClient } from '@prisma/client';

export async function seedRoles(prisma: PrismaClient) {
  const roles = [
    {
      name: 'USER',
    },
    {
      name: 'ADMIN',
    },
    {
      name: 'SUPER_ADMIN',
    }
  ];

  for (const role of roles) {
    await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role
    });
  }

  console.log('Roles seeded successfully');
} 