import { ApiProperty } from '@nestjs/swagger';
import { IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { toBigInt } from '../../common/utils/bigint.util';

export class UpdateUserPermissionsDto {
  @ApiProperty({
    type: [Number],
    description: 'Array of permission IDs',
    example: [1, 2, 3]
  })
  @IsArray()
  @Transform(({ value }) => value.map((id: string) => toBigInt(id)))
  permissionIds: bigint[];
} 