#!/bin/bash

echo "Starting deployment..."

# Pull latest changes
echo "Pulling latest changes..."
if ! git pull; then
    echo "Failed to pull changes"
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
if ! npm install; then
    echo "Failed to install dependencies"
    exit 1
fi

# Restart PM2 process
echo "Restarting PM2 process..."
if ! pm2 restart 0; then
    echo "Failed to restart PM2 process"
    exit 1
fi

echo "Deployment completed successfully!" 