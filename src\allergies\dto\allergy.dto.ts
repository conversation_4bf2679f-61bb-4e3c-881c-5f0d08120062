import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsDate } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { toBigInt } from '../../common/utils/bigint.util';

export class CreateAllergyDto {
  @ApiProperty({ example: 'Peanuts' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Arachides' })
  @IsString()
  name_fr: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  bannedIngredients?: bigint[];
}

export class UpdateAllergyDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ example: 'Peanuts', required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ example: 'Arachides', required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  bannedIngredients?: bigint[];
} 