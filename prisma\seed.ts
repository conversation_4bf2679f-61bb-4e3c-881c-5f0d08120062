import { PrismaClient } from '@prisma/client';
import { seedRoles } from './seeders/roles.seeder';
import { seedRegions } from './seeders/regions.seeder';
const prisma = new PrismaClient();

async function main() {
  // Seed roles first
  await seedRoles(prisma);
  await seedRegions(prisma);
  
  // Then seed permissions and assign to roles
  // await seedPermissions(prisma);
  // await seedUnits(prisma);
  /*
  // Then seed users
  await seedUsers(prisma);
    */
  // Then seed other data
  // await seedCategories(prisma);
  // await seedIngredients(prisma);
  // await seedDiets(prisma);
  // await seedAllergies(prisma);
  // await seedNutritionElements(prisma);

}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 