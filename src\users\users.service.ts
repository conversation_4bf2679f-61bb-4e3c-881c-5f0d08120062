import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from './dto/user.dto';
import { BaseService } from '../common/services/base.service';
import { Prisma } from '@prisma/client';
import { UserStatus } from '../auth/enums/user-status.enum';
import * as bcrypt from 'bcrypt';
import { Role } from '../auth/enums/roles.enum';
import * as crypto from 'crypto';
import { MailerService } from '../common/services/mailer.service';
import { UpdateUserPermissionsDto } from './dto/user-permission.dto';

@Injectable()
export class UsersService extends BaseService {
  constructor(
    private prisma: PrismaService,
    private mailerService: MailerService
  ) {
    super();
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    onlyDeleted?: boolean;
    order?: 'asc' | 'desc';
    status?: UserStatus;
    role?: Role;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      onlyDeleted = false,
      order = 'desc',
      status,
      role
    } = query;

    const skip = (page - 1) * limit;

    const where: Prisma.UserWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      NOT: {
        roles: {
          some: {
            role: {
              name: "SUPER_ADMIN"
            }
          }
        }
      },
      ...(search && {
        OR: [
          { email: { contains: search, mode: 'insensitive' } },
          { fullName: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(status && { status }),
      ...(role && {
        roles: {
          some: {
            role: {
              name: role
            }
          }
        }
      })
    };

    const [items, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: Number(limit),
        select: {
          id: true,
          email: true,
          fullName: true,
          phoneNumber: true,
          avatar: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          deletedAt: true,
          roles: {
            select: {
              role: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          id: order
        }
      }),
      this.prisma.user.count({ where })
    ]);

    const transformedItems = items.map(user => ({
      ...user,
      roles: user.roles.map(r => r.role.name),
      _count: undefined
    }));

    return {
      items: transformedItems,
      total,
      page,
      limit,
      hasNextPage: skip + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const user = await this.prisma.user.findFirst({
      where: {
        id,
        deletedAt: null,
        NOT: {
          roles: {
            some: {
              role: { name: Role.SUPER_ADMIN }
            }
          }
        }
      },
      select: {
        id: true,
        email: true,
        fullName: true,
        phoneNumber: true,
        avatar: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        roles: {
          select: {
            role: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Transform the roles to be a simple array of role names
    return {
      ...user,
      roles: user.roles.map(r => r.role.name)
    };
  }


  
  async create(dto: CreateUserDto) {
    // Check if user exists
    const exists = await this.prisma.user.findUnique({
      where: { email: dto.email }
    });

    if (exists) {
      throw new ConflictException('Email already registered');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(dto.password, 10);

    const data: Prisma.UserCreateInput = {
      email: dto.email,
      password: hashedPassword,
      fullName: dto.fullName,
      phoneNumber: dto.phoneNumber,
      status: dto.status || UserStatus.ACTIVE,
      roles: {
        create: (dto.roles || [Role.USER]).map(roleName => ({
          role: {
            connect: {
              name: roleName
            }
          }
        }))
      }
    };

    return this.prisma.user.create({
      data,
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: true
              }
            }
          }
        }
      }
    });
  }

  async update(id: bigint, dto: UpdateUserDto) {
    const user = await this.prisma.user.findFirst({
      where: { id }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Prepare the update data
    const updateData: any = {
      fullName: dto.fullName,
      phoneNumber: dto.phoneNumber,
      status: dto.status,
      deletedAt: dto.deletedAt
    };

    // If roles are provided, update them
    if (dto.roles) {
      // Delete existing roles
      await this.prisma.userRole.deleteMany({
        where: { userId: id }
      });

      // Add new roles
      updateData.roles = {
        create: dto.roles.map(roleName => ({
          role: {
            connect: { name: roleName }
          }
        }))
      };
    }

    return this.prisma.user.update({
      where: { id: BigInt(id) },
      data: updateData,
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: true
              }
            }
          }
        }
      }
    });
  }

  async remove(id: bigint) {
    // check if the user is not admin he can only delete himself
    const user = await this.findOne(id);

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Soft delete by setting deletedAt
    // return this.prisma.user.update({
    //   where: { id },
    //   data: { 
    //     deletedAt: new Date(),
    //     status: 'INACTIVE'  // Optional: also update status
    //   }
    // });
    // hard delete user
    return this.prisma.user.delete({
      where: { id }
    });
  }

  async resetUserPassword(userId: bigint) {
    const user = await this.prisma.user.findFirst({
      where: { 
        id: userId,
        deletedAt: null
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get password from email prefix
    const newPassword = user.email.split('@')[0];
    
    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update user's password
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword }
    });

    // Send email with new password
    const emailSent = await this.mailerService.sendPasswordResetNotification(
      user.email,
      newPassword
    );

    return { 
      message: emailSent 
        ? 'Password reset successfully. New password sent to user\'s email.'
        : 'Password reset successfully but failed to send email.',
      emailSent: emailSent  // Just return the boolean value
    };
  }

  async updateUserPermissions(userId: bigint, dto: UpdateUserPermissionsDto) {
    // Check if user exists and is not SUPER_ADMIN
    const user = await this.findOne(userId);

    // Validate permissions exist
    const existingPermissions = await this.prisma.permission.findMany({
      where: {
        id: {
          in: dto.permissionIds
        }
      }
    });

    if (existingPermissions.length !== dto.permissionIds.length) {
      const foundIds = existingPermissions.map(p => p.id.toString());
      const invalidIds = dto.permissionIds
        .map(id => id.toString())
        .filter(id => !foundIds.includes(id));
      throw new BadRequestException(`Invalid permission IDs: ${invalidIds.join(', ')}`);
    }

    // Start transaction
    return this.prisma.$transaction(async (tx) => {
      // Remove existing permissions
      await tx.userPermission.deleteMany({
        where: { userId }
      });

      // Add new permissions if any
      if (dto.permissionIds.length > 0) {
        await tx.userPermission.createMany({
          data: dto.permissionIds.map(permissionId => ({
            userId,
            permissionId
          }))
        });
      }

      // Return updated user with permissions
      return tx.user.findFirst({
        where: { 
          id: userId,
          deletedAt: null
        },
        include: {
          permissions: {
            include: {
              permission: true
            }
          }
        }
      });
    });
  }

  async getUserPermissions(userId: bigint) {
    const user = await this.prisma.user.findFirst({
      where: { 
        id: userId,
        deletedAt: null,
        NOT: {
          roles: {
            some: {
              role: { name: Role.SUPER_ADMIN }
            }
          }
        }
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return user.permissions.map(up => up.permission);
  }
} 