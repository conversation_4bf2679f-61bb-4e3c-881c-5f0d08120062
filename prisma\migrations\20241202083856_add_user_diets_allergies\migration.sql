-- CreateTable
CREATE TABLE "UserDiet" (
    "id" BIGSERIAL NOT NULL,
    "userId" BIGINT NOT NULL,
    "dietId" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserDiet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAllergy" (
    "id" BIGSERIAL NOT NULL,
    "userId" BIGINT NOT NULL,
    "allergyId" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserAllergy_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserDiet_userId_dietId_key" ON "UserDiet"("userId", "dietId");

-- CreateIndex
CREATE UNIQUE INDEX "UserAllergy_userId_allergyId_key" ON "UserAllergy"("userId", "allergyId");

-- AddForeignKey
ALTER TABLE "UserDiet" ADD CONSTRAINT "UserDiet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserDiet" ADD CONSTRAINT "UserDiet_dietId_fkey" FOREIGN KEY ("dietId") REFERENCES "diets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAllergy" ADD CONSTRAINT "UserAllergy_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAllergy" ADD CONSTRAINT "UserAllergy_allergyId_fkey" FOREIGN KEY ("allergyId") REFERENCES "allergies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
