import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, Matches, MinLength } from 'class-validator';

export class CheckPhoneDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number with country code'
  })
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format (e.g., +1234567890)'
  })
  phone: string;

  @ApiProperty({
    example: 'en',
    description: 'Locale',
    required: false
  })
  @IsString()
  @IsOptional()
  locale?: string;
}

export class PhonePasswordLoginDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number with country code'
  })
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format (e.g., +1234567890)'
  })
  phone: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password'
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: 'en',
    description: 'Locale',
    required: false
  })
  @IsString()
  @IsOptional()
  locale?: string;
}

export class RequestOtpDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number with country code'
  })
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format (e.g., +1234567890)'
  })
  phone: string;

  @ApiProperty({
    example: 'en',
    description: 'Locale',
    required: false
  })
  @IsString()
  @IsOptional()
  locale?: string;
}

export class VerifyOtpDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number with country code'
  })
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Phone number must be in E.164 format'
  })
  phone: string;

  @ApiProperty({
    example: '123456',
    description: 'OTP code received'
  })
  @IsString()
  @Matches(/^\d{6}$/, {
    message: 'OTP must be 6 digits'
  })
  otp: string;

  @ApiProperty({
    example: 'en',
    description: 'Locale',
    required: false
  })
  @IsString()
  @IsOptional()
  locale?: string;
}

export class SetPasswordDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Verification token received after OTP verification'
  })
  @IsString()
  verificationToken: string;

  @ApiProperty({
    example: 'password123',
    description: 'New password for the user'
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Full name of the user',
    required: false
  })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({
    example: 'en',
    description: 'Locale',
    required: false
  })
  @IsString()
  @IsOptional()
  locale?: string;
}

// Legacy DTOs for backward compatibility
export class ClientLoginDto extends RequestOtpDto {}

// Response DTOs
export class CheckPhoneResponseDto {
  @ApiProperty({
    example: true,
    description: 'Whether user exists with this phone number'
  })
  userExists: boolean;

  @ApiProperty({
    example: 'User found',
    description: 'Response message'
  })
  message: string;
}