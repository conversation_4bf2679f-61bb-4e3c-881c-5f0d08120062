import { <PERSON>, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ClientAuthService } from './client-auth.service';
import {
  CheckPhoneDto,
  PhonePasswordLoginDto,
  RequestOtpDto,
  VerifyOtpDto,
  SetPasswordDto,
  CheckPhoneResponseDto,
  ClientLoginDto
} from './dto/client-auth.dto';

@ApiTags('Client Authentication')
@Controller('client')
export class ClientAuthController {
  constructor(private readonly clientAuthService: ClientAuthService) {}

  // 1. Check if user exists by phone number
  @Post('check-phone')
  @ApiOperation({ summary: 'Check if user exists by phone number' })
  @ApiResponse({ status: 200, description: 'Phone check completed', type: CheckPhoneResponseDto })
  async checkPhone(@Body() dto: CheckPhoneDto) {
    return this.clientAuthService.checkPhone(dto);
  }

  // 2. Login with phone and password (for existing users)
  @Post('login-password')
  @ApiOperation({ summary: 'Login with phone and password for existing users' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async loginWithPassword(@Body() dto: PhonePasswordLoginDto) {
    return this.clientAuthService.loginWithPhonePassword(dto);
  }

  // 3. Request OTP for new users
  @Post('request-otp')
  @ApiOperation({ summary: 'Request OTP for new user registration' })
  @ApiResponse({ status: 200, description: 'OTP sent successfully' })
  async requestOtp(@Body() dto: RequestOtpDto) {
    return this.clientAuthService.requestOtp(dto);
  }

  // 4. Verify OTP (for new users only)
  @Post('verify-otp')
  @ApiOperation({
    summary: 'Verify OTP for new user registration',
    description: 'Verifies OTP and returns a verification token needed for setting password. Token expires in 30 minutes.'
  })
  @ApiResponse({ status: 200, description: 'OTP verified successfully, returns verification token' })
  @ApiResponse({ status: 400, description: 'User already exists' })
  @ApiResponse({ status: 401, description: 'Invalid or expired OTP' })
  async verifyOtp(@Body() dto: VerifyOtpDto) {
    return this.clientAuthService.verifyOtp(dto);
  }

  // 5. Set password after OTP verification (Protected with verification token)
  @Post('set-password')
  @ApiOperation({
    summary: 'Set password for new user after OTP verification',
    description: 'Requires verification token from successful OTP verification. Creates user account and returns access token.'
  })
  @ApiResponse({ status: 200, description: 'Account created successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired verification token' })
  @ApiResponse({ status: 400, description: 'User already exists' })
  async setPassword(@Body() dto: SetPasswordDto) {
    return this.clientAuthService.setPassword(dto);
  }

  // Legacy endpoints for backward compatibility
  @Post('login')
  @ApiOperation({ summary: '[DEPRECATED] Request OTP for mobile app login - use request-otp instead' })
  @ApiResponse({ status: 200, description: 'OTP sent successfully' })
  async login(@Body() dto: ClientLoginDto) {
    return this.clientAuthService.login(dto);
  }
}