/*
  Warnings:

  - You are about to drop the column `description` on the `allergies` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `allergies` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name_en]` on the table `allergies` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name_fr]` on the table `allergies` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `name_en` to the `allergies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_fr` to the `allergies` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "allergies_name_key";

-- AlterTable
ALTER TABLE "allergies" DROP COLUMN "description",
DROP COLUMN "name",
ADD COLUMN     "description_en" TEXT,
ADD COLUMN     "description_fr" TEXT,
ADD COLUMN     "name_en" TEXT NOT NULL,
ADD COLUMN     "name_fr" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "allergies_name_en_key" ON "allergies"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "allergies_name_fr_key" ON "allergies"("name_fr");
