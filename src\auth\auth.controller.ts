import { Controller, Post, Body, Get, UseGuards, UseInterceptors, UploadedFile, ParseFilePipe, MaxFileSizeValidator, FileTypeValidator, Query, Patch, Delete, Request, BadRequestException } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ConfigService } from '@nestjs/config';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiConsumes,
  ApiBody,
  ApiTooManyRequestsResponse,
  ApiHeader,
  ApiProperty
} from '@nestjs/swagger';
import { Throttle, SkipThrottle } from '@nestjs/throttler';
import { AuthService } from './auth.service';
import { LoginDto, RegisterDto, TokenResponse, RefreshTokenDto, ForgotPasswordDto, ResetPasswordDto } from './dto/auth.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { UploadService } from '../common/services/upload.service';
import * as multer from 'multer';
import { extname } from 'path';
import { ProfileResponse } from './dto/profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';

// Create a DTO for file upload request
class FileUploadDto {
  @ApiProperty({
    type: 'file',
    format: 'binary',
    description: 'Avatar image file (max 2MB, formats: jpg, jpeg, png)',
    required: true
  })
  avatar: Express.Multer.File;
}

// Create a response DTO for avatar upload
class AvatarResponseDto {
  @ApiProperty({
    example: 'http://localhost:3000/uploads/avatars/1234567890.jpg',
    description: 'URL of the uploaded avatar'
  })
  avatarUrl: string;
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private uploadService: UploadService,
    private configService: ConfigService
  ) {}

  @Post('register')
  @Throttle({ default: { limit: 3, ttl: 60 } })  // 3 requests per minute
  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({ status: 201, type: TokenResponse })
  @ApiTooManyRequestsResponse({ 
    description: 'Too many registration attempts. Please try again later.' 
  })
  async register(@Body() dto: RegisterDto) {
    return this.authService.register(dto);
  }

  @Post('login')
  @Throttle({ default: { limit: 5, ttl: 60 } })  // 5 requests per minute
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, type: TokenResponse })
  @ApiTooManyRequestsResponse({ 
    description: 'Too many login attempts. Please try again later.' 
  })
  async login(@Body() dto: LoginDto) {
    return this.authService.login(dto);
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Token refreshed successfully',
    type: TokenResponse 
  })
  @ApiUnauthorizedResponse({ description: 'Invalid refresh token' })
  @ApiBody({ 
    type: RefreshTokenDto,
    description: 'Refresh token data',
    required: true 
  })
  async refresh(@Body() dto: RefreshTokenDto) {
    return this.authService.refreshToken(dto.refresh_token);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 200, description: 'Logged out successfully' })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  async logout(@CurrentUser() user) {
    await this.authService.revokeAllUserTokens(BigInt(user.id));
    return { message: 'Logged out successfully' };
  }

  @Post('upload-avatar')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: 'Upload user avatar',
    description: 'Upload a new avatar image for the authenticated user. Supports jpg, jpeg, and png formats up to 2MB.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      required: ['avatar'],
      properties: {
        avatar: {
          type: 'string',
          format: 'binary',
          description: 'Avatar image file (max 2MB, formats: jpg, jpeg, png)'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Avatar uploaded successfully',
    type: AvatarResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid file format or size'
  })
  @ApiUnauthorizedResponse({ 
    description: 'Not authenticated or invalid token'
  })
  async uploadAvatar(
    @CurrentUser() user,
    @Request() req
  ) {
    const data = await req.file();
    if (!data) {
      throw new BadRequestException('No file uploaded');
    }

    if (!data.mimetype.match(/^image\/(jpeg|jpg|png)$/)) {
      throw new BadRequestException('Invalid file type. Only JPG, JPEG and PNG are allowed');
    }

    if (data.file.length > 2 * 1024 * 1024) { // 2MB
      throw new BadRequestException('File too large. Maximum size is 2MB');
    }

    const filePath = await this.uploadService.uploadImage(
      {
        filename: data.filename,
        encoding: data.encoding,
        mimetype: data.mimetype,
        buffer: await data.toBuffer()
      },
      'avatars'
    );

    const avatarUrl = `/uploads/${filePath}`;
    await this.authService.updateAvatar(user.id, avatarUrl);

    return { avatarUrl };
  }

  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired verification token' })
  async verifyEmail(@Query('token') token: string) {
    return this.authService.verifyEmail(token);
  }

  @Post('forgot-password')
  @Throttle({ default: { limit: 3, ttl: 60 } })  // 3 requests per minute
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ 
    status: 200,
    description: 'Password reset email sent if account exists'
  })
  @ApiTooManyRequestsResponse({
    description: 'Too many password reset attempts. Please try again later.'
  })
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return this.authService.forgotPassword(dto.email);
  }

  @Post('reset-password')
  @Throttle({ default: { limit: 3, ttl: 60 } })
  @ApiOperation({ summary: 'Reset password using token' })
  @ApiResponse({ 
    status: 200,
    description: 'Password reset successful'
  })
  @ApiResponse({ 
    status: 401,
    description: 'Invalid or expired reset token'
  })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.authService.resetPassword(dto.token, dto.password);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get user profile with diets and allergies' })
  @ApiResponse({ 
    status: 200, 
    description: 'User profile with preferences',
    type: ProfileResponse
  })
  @ApiBearerAuth('JWT-auth')
  async getProfile(@CurrentUser() user) {
    return this.authService.getProfileWithPreferences(user.id);
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ 
    status: 200, 
    description: 'Profile updated successfully',
    type: ProfileResponse
  })
  @ApiBearerAuth('JWT-auth')
  async updateProfile(
    @CurrentUser() user,
    @Body() dto: UpdateProfileDto
  ) {
    return this.authService.updateProfile(user.id, dto);
  }

  @Delete('profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete user account (soft delete)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Account deleted successfully'
  })
  @ApiBearerAuth('JWT-auth')
  async deleteAccount(@CurrentUser() user) {
    return this.authService.deleteAccount(user.id);
  }
} 