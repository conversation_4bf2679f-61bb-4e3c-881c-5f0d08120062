import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from './dto/user.dto';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { toBigInt } from '../common/utils/bigint.util';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { UpdateUserPermissionsDto } from './dto/user-permission.dto';
import { ParseBigIntPipe } from '../common/pipes/parse-bigint.pipe';
import { UserStatus } from '../auth/enums/user-status.enum';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN, Role.SUPER_ADMIN, Role.USER)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: 'Create new user (Admin or Super Admin)' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return all users' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted users. If false, returns only active users.'
  })
  @ApiQuery({ 
    name: 'order', 
    required: false, 
    enum: ['asc', 'desc'], 
    description: 'Sort order by ID' 
  })
  @ApiQuery({ 
    name: 'status', 
    required: false, 
    enum: UserStatus,
    description: 'Filter users by status' 
  })
  @ApiQuery({ 
    name: 'role', 
    required: false, 
    enum: [Role.ADMIN, Role.USER],
    description: 'Filter users by role (excluding SUPER_ADMIN)' 
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('onlyDeleted') onlyDeleted?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('status') status?: UserStatus,
    @Query('role') role?: Role
  ) {
    if (role === Role.SUPER_ADMIN) {
      role = undefined;
    }
    
    return this.usersService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      onlyDeleted: onlyDeleted === 'true',
      order,
      status,
      role
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by id (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return the user',
    schema: {
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        fullName: { type: 'string', nullable: true },
        phoneNumber: { type: 'string', nullable: true },
        avatar: { type: 'string', nullable: true },
        status: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time', nullable: true },
        roles: { 
          type: 'array',
          items: { type: 'string', enum: Object.values(Role) }
        }
      }
    }
  })
  findOne(@Param('id') id: string) {
    return this.usersService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update user (Admin or Super Admin)' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateUserDto) {
    return this.usersService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a user' })
  @ApiResponse({ status: 200, description: 'User successfully deleted (soft)' })
  @Roles(Role.ADMIN,Role.USER, Role.SUPER_ADMIN)
  remove(@Param('id', ParseBigIntPipe) id: bigint) {
    return this.usersService.remove(id);
  }

  @Patch('reset/:userId')
  @ApiOperation({ 
    summary: 'Reset user password',
    description: 'Resets a user\'s password using their email prefix and sends it to their email.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Password reset successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Password reset successfully. New password sent to user\'s email.'
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async resetPassword(@Param('userId') userId: string) {
    return this.usersService.resetUserPassword(toBigInt(userId));
  }

  @Patch(':id/permissions')
  @ApiOperation({ summary: 'Update user permissions' })
  @ApiResponse({ 
    status: 200, 
    description: 'User permissions updated successfully'
  })
  async updatePermissions(
    @Param('id') id: string,
    @Body() dto: UpdateUserPermissionsDto
  ) {
    return this.usersService.updateUserPermissions(toBigInt(id), dto);
  }

  @Get(':id/permissions')
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiResponse({ 
    status: 200, 
    description: 'User permissions retrieved successfully'
  })
  async getPermissions(@Param('id') id: string) {
    return this.usersService.getUserPermissions(toBigInt(id));
  }
} 