import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsBoolean, IsNumber, IsDate } from 'class-validator';
import { UnitType } from '@prisma/client';
import { Type } from 'class-transformer';

export class CreateUnitDto {
  @ApiProperty({ example: 'Gram' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Gramme' })
  @IsString()
  name_fr: string;

  @ApiProperty({ example: 'g' })
  @IsString()
  symbol: string;

  @ApiProperty({ enum: UnitType })
  @IsEnum(UnitType)
  type: UnitType;

  @ApiProperty({ required: false, default: false })
  @IsOptional()
  @IsBoolean()
  baseUnit?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  conversionFactor?: number;
}

export class UpdateUnitDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ example: 'Gram', required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ example: 'Gramme', required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  symbol?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(UnitType)
  type?: UnitType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  baseUnit?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  conversionFactor?: number;
} 