import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum } from 'class-validator';
import { LinkType } from '@prisma/client';

export class LinkDto {
  @ApiProperty({ 
    enum: LinkType,
    example: LinkType.IMAGE
  })
  @IsEnum(LinkType)
  type: LinkType;

  @ApiProperty({ example: 'https://example.com/image.jpg' })
  @IsString()
  url: string;
}

// These are for backwards compatibility, extending the base LinkDto
export class CreateLinkDto extends LinkDto {}
export class UpdateLinkDto extends LinkDto {} 