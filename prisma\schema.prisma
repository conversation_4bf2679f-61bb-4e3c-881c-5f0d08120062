generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                  BigInt               @id @default(autoincrement())
  email               String               @unique
  password            String
  fullName            String?
  phoneNumber         String?              @unique
  avatar              String?
  status              String               @default("ACTIVE")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  emailVerifiedAt     DateTime?
  deletedAt           DateTime?
  fcmToken            String?              @map("fcm_token")
  allergies           UserAllergy[]
  diets               UserDiet[]
  verificationToken   VerificationToken[]
  passwordResetTokens PasswordResetToken[]
  recipeLikes         RecipeLike[]
  recipeReviews       RecipeReview[]
  recipeViews         RecipeView[]
  recipeWishlists     RecipeWishlist[]
  refreshTokens       RefreshToken[]
  permissions         UserPermission[]
  roles               UserRole[]

  @@map("users")
}

model Role {
  id          BigInt       @id @default(autoincrement())
  name        String       @unique
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  users       UserRole[]
  permissions Permission[] @relation("PermissionToRole")

  @@map("roles")
}

model Permission {
  id        BigInt           @id @default(autoincrement())
  name      String           @unique
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  users     UserPermission[]
  roles     Role[]           @relation("PermissionToRole")

  @@map("permissions")
}

model UserRole {
  userId     BigInt
  roleId     BigInt
  assignedAt DateTime @default(now())
  role       Role     @relation(fields: [roleId], references: [id])
  user       User     @relation(fields: [userId], references: [id])

  @@id([userId, roleId])
  @@map("user_roles")
}

model RefreshToken {
  id        BigInt    @id @default(autoincrement())
  token     String    @unique
  userId    BigInt
  expiresAt DateTime
  createdAt DateTime  @default(now())
  revokedAt DateTime?
  user      User      @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("refresh_tokens")
}

model VerificationToken {
  id        BigInt   @id @default(autoincrement())
  token     String   @unique
  userId    BigInt
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([token])
}

model PasswordResetToken {
  id        BigInt    @id @default(autoincrement())
  token     String    @unique
  userId    BigInt
  expiresAt DateTime
  createdAt DateTime  @default(now())
  usedAt    DateTime?
  user      User      @relation(fields: [userId], references: [id])

  @@index([token])
  @@map("password_reset_tokens")
}

model Unit {
  id                BigInt             @id @default(autoincrement())
  symbol            String             @unique
  type              UnitType
  baseUnit          Boolean            @default(false)
  conversionFactor  Float?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  deletedAt         DateTime?
  name_en           String             @unique
  name_fr           String             @unique
  recipeIngredients RecipeIngredient[]

  @@map("units")
}

model UserOtp {
  id        BigInt   @id @default(autoincrement())
  phone     String   @unique
  otp       String
  expiresAt DateTime
  createdAt DateTime @default(now())
  attempts  Int      @default(0)
  verified  Boolean  @default(false)

  @@index([phone])
  @@map("user_otps")
}

model Diet {
  id                BigInt     @id @default(autoincrement())
  image             String?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  bannedIngredients BigInt[]
  deletedAt         DateTime?
  description_en    String?
  description_fr    String?
  name_en           String     @unique
  name_fr           String     @unique
  users             UserDiet[]

  @@map("diets")
}

model Allergy {
  id                BigInt        @id @default(autoincrement())
  image             String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  bannedIngredients BigInt[]
  deletedAt         DateTime?
  description_en    String?
  description_fr    String?
  name_en           String        @unique
  name_fr           String        @unique
  users             UserAllergy[]

  @@map("allergies")
}

model Category {
  id             BigInt    @id @default(autoincrement())
  image          String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
  description_en String?
  description_fr String?
  name_en        String    @unique
  name_fr        String    @unique
  recipes        Recipe[]

  @@map("categories")
}

model Ingredient {
  id             BigInt             @id @default(autoincrement())
  image          String?
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  deletedAt      DateTime?
  name_en        String
  name_fr        String
  type_en        String?
  type_fr        String?
  description_en String?
  description_fr String?
  recipes        RecipeIngredient[]

  @@map("ingredients")
}

model Cuisine {
  id        BigInt    @id @default(autoincrement())
  name_en   String
  name_fr   String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  deletedAt DateTime?
  image     String?
  recipes   Recipe[]

  @@map("cuisines")
}

model Recipe {
  id                    BigInt             @id @default(autoincrement())
  preparationTime       Int
  categoryId            BigInt
  notPermittedDiets     BigInt[]
  notPermittedAllergies BigInt[]
  isActive              Boolean            @default(true)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  deletedAt             DateTime?
  portions              Int                @default(1)
  difficultyLevel_en    String
  difficultyLevel_fr    String
  name_en               String
  name_fr               String
  shortDescription_en   String?
  shortDescription_fr   String?
  cuisineId             BigInt             @default(15)
  cookingTime           Int
  imageUrl              String?
  links                 Link[]
  ingredients           RecipeIngredient[]
  likes                 RecipeLike[]
  nutritionFacts        RecipeNutrition[]
  reviews               RecipeReview[]
  steps                 RecipeStep[]
  views                 RecipeView[]
  wishlists             RecipeWishlist[]
  category              Category           @relation(fields: [categoryId], references: [id])
  cuisine               Cuisine            @relation(fields: [cuisineId], references: [id])
  isDemo                Int                @default(0)

  @@map("recipes")
}

model NutritionElement {
  id        BigInt            @id @default(autoincrement())
  unit      String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  deletedAt DateTime?
  name_en   String            @unique
  name_fr   String            @unique
  recipes   RecipeNutrition[]

  @@map("nutrition_elements")
}

model RecipeIngredient {
  id           BigInt     @id @default(autoincrement())
  recipeId     BigInt
  ingredientId BigInt
  quantity     Float
  unitId       BigInt
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  ingredient   Ingredient @relation(fields: [ingredientId], references: [id])
  recipe       Recipe     @relation(fields: [recipeId], references: [id])
  unit         Unit       @relation(fields: [unitId], references: [id])

  @@map("recipe_ingredients")
}

model RecipeStep {
  id         BigInt   @id @default(autoincrement())
  recipeId   BigInt
  order      Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  content_en String
  content_fr String
  recipe     Recipe   @relation(fields: [recipeId], references: [id])

  @@map("recipe_steps")
}

model RecipeNutrition {
  id                 BigInt           @id @default(autoincrement())
  recipeId           BigInt
  nutritionElementId BigInt
  value              Float
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  nutritionElement   NutritionElement @relation(fields: [nutritionElementId], references: [id])
  recipe             Recipe           @relation(fields: [recipeId], references: [id])

  @@map("recipe_nutrition")
}

model GeneralSetting {
  id        BigInt   @id @default(autoincrement())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("general_settings")
}

model Link {
  id        BigInt   @id @default(autoincrement())
  type      LinkType
  url       String
  recipeId  BigInt
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  recipe    Recipe   @relation(fields: [recipeId], references: [id])

  @@map("links")
}

model UserDiet {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  dietId    BigInt
  createdAt DateTime @default(now())
  diet      Diet     @relation(fields: [dietId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, dietId])
}

model UserAllergy {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  allergyId BigInt
  createdAt DateTime @default(now())
  allergy   Allergy  @relation(fields: [allergyId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, allergyId])
}

model UserPermission {
  id           BigInt     @id @default(autoincrement())
  userId       BigInt
  permissionId BigInt
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  permission   Permission @relation(fields: [permissionId], references: [id])
  user         User       @relation(fields: [userId], references: [id])

  @@unique([userId, permissionId])
  @@map("user_permissions")
}

model RecipeLike {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  recipeId  BigInt
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  recipe    Recipe   @relation(fields: [recipeId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, recipeId])
  @@map("recipe_likes")
}

model RecipeWishlist {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  recipeId  BigInt
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  recipe    Recipe   @relation(fields: [recipeId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, recipeId])
  @@map("recipe_wishlists")
}

model RecipeView {
  id        BigInt    @id @default(autoincrement())
  userId    BigInt
  recipeId  BigInt
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  recipe    Recipe    @relation(fields: [recipeId], references: [id])
  user      User      @relation(fields: [userId], references: [id])

  @@map("recipe_views")
}

model RecipeReview {
  id           BigInt       @id @default(autoincrement())
  userId       BigInt
  recipeId     BigInt
  review_title String
  review_body  String
  status       ReviewStatus @default(OPEN)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  deletedAt    DateTime?
  recipe       Recipe       @relation(fields: [recipeId], references: [id])
  user         User         @relation(fields: [userId], references: [id])

  @@map("recipe_reviews")
}

model Notification {
  id          BigInt   @id @default(autoincrement())
  title       String   @db.VarChar(255)
  description String
  image       String?  @db.VarChar(255)
  users       BigInt[]
  isAll       Boolean  @default(false) @map("is_all")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @map("updated_at")

  @@index([createdAt])
  @@map("notifications")
}

model ActivationCode {
  id        BigInt   @id @default(autoincrement())
  code      String   @unique
  regionId  BigInt
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
  region    Region   @relation(fields: [regionId], references: [id])
}

model Order {
  id        BigInt   @id @default(autoincrement())
  orderNumber String @unique
  orderDate DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
}

model UserAccess {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt
  orderId   BigInt
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
}

model Region {
  id        BigInt   @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
  activationCodes ActivationCode[]
}

enum UnitType {
  WEIGHT
  VOLUME
  QUANTITY
  LENGTH
  TEMPERATURE
}

enum LinkType {
  LINK
  IMAGE
  VIDEO_URL
}

enum ReviewStatus {
  OPEN
  IN_REVIEW
  SOLVED
}


