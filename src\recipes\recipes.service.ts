import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRecipeDto, UpdateRecipeDto } from './dto/recipe.dto';
import { DifficultyLevel } from './enums/difficulty-level.enum';
import { toBigInt } from '../common/utils/bigint.util';
import { Prisma } from '@prisma/client';
import PDFDocument from 'pdfkit';
import * as fs from 'fs';
import * as path from 'path';
import { CreateReviewDto } from './dto/review.dto';

type RecipeSelect = {
  id: true;
  name_en: true;
  name_fr: true;
  shortDescription_en: true;
  shortDescription_fr: true;
  preparationTime: true;
  portions: true;
  difficultyLevel_en: true;
  difficultyLevel_fr: true;
  categoryId: true;
  cuisineId: true;
  notPermittedDiets: true;
  notPermittedAllergies: true;
  isActive: true;
  createdAt: true;
  updatedAt: true;
  deletedAt: true;
  // category: {
  //   select: {
  //     id: true;
  //     name_en: true;
  //     name_fr: true;
  //     description_en: true;
  //     description_fr: true;
  //     image: true;
  //   }
  // };
  // cuisine: {
  //   select: {
  //     id: true;
  //     name_en: true;
  //     name_fr: true;
  //     image: true
  //   }
  // };
  ingredients: {
    select: {
      ingredient: {
        select: {
          id: true;
          name_en: true;
          name_fr: true;
          type_en: true;
          type_fr: true;
          description_en: true;
          description_fr: true;
          image: true
        }
      };
      quantity: true;
      unit: {
        select: {
          id: true;
          name_en: true;
          name_fr: true;
          symbol: true;
        }
      }
    }
  };
  steps: {
    select: {
      order: true;
      content_en: true;
      content_fr: true;
    };
    orderBy: {
      order: 'asc';
    };
  };
  nutritionFacts: {
    select: {
      nutritionElement: {
        select: {
          id: true;
          name_en: true;
          name_fr: true;
          unit: true;
        }
      };
      value: true;
    };
  };
  links: {
    select: {
      type: true;
      url: true;
    };
  };
  _count: {
    select: {
      likes: true;
      wishlists: true;
      views: true;
    };
  };
  likes: true;
  wishlists: true;
};

type RecipeWithCounts = {
  id: bigint;
  name_en: string;
  name_fr: string;
  shortDescription_en: string;
  shortDescription_fr: string;
  preparationTime: number;
  portions: number;
  difficultyLevel_en: string;
  difficultyLevel_fr: string;
  category: {
    id: bigint;
    name_en: string;
    name_fr: string;
    // description_en: string;
    // description_fr: string;
    // image: string;
  };
  // cuisine: {
  //   id: bigint;
  //   name_en: string;
  //   name_fr: string;
  //   image: string;
  // };
  ingredients: Array<{
    ingredient: {
      id: bigint;
      name_en: string;
      name_fr: string;
      description_en: string;
      description_fr: string;
      image: string;
    };
    quantity: number;
    unit: {
      id: bigint;
      name_en: string;
      name_fr: string;
      symbol: string;
    };
  }>;
  _count: {
    likes: number;
    wishlists: number;
    views: number;
  };
  likes: Array<{
    userId: bigint; id: bigint 
}>;
  wishlists: Array<{
    userId: bigint; id: bigint 
}>;
};

@Injectable()
export class RecipesService {
  constructor(private prisma: PrismaService) {}

  private async validateCategory(categoryId: bigint) {
    const category = await this.prisma.category.findFirst({
      where: {
        id: categoryId,
        deletedAt: null
      }
    });

    if (!category) {
      throw new BadRequestException(`Category with ID ${categoryId} not found`);
    }
  }

  private async validateIngredients(ingredients: { ingredientId: bigint; unitId: bigint }[]) {
    if (!ingredients?.length) return;

    // Check ingredients
    const ingredientIds = ingredients.map(i => i.ingredientId);
    const validIngredients = await this.prisma.ingredient.findMany({
      where: {
        id: { in: ingredientIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const invalidIngredientIds = ingredientIds.filter(
      id => !validIngredients.find(i => i.id === id)
    );

    if (invalidIngredientIds.length > 0) {
      throw new BadRequestException(`Invalid ingredient IDs: ${invalidIngredientIds.join(', ')}`);
    }

    // Check units
    const unitIds = ingredients.map(i => i.unitId);
    const validUnits = await this.prisma.unit.findMany({
      where: {
        id: { in: unitIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const invalidUnitIds = unitIds.filter(
      id => !validUnits.find(u => u.id === id)
    );

    if (invalidUnitIds.length > 0) {
      throw new BadRequestException(`Invalid unit IDs: ${invalidUnitIds.join(', ')}`);
    }
  }

  private async validateNutritionElements(nutritionElements: { nutritionElementId: bigint }[]) {
    if (!nutritionElements?.length) return;

    const elementIds = nutritionElements.map(n => n.nutritionElementId);
    const validElements = await this.prisma.nutritionElement.findMany({
      where: {
        id: { in: elementIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const invalidIds = elementIds.filter(
      id => !validElements.find(e => e.id === id)
    );

    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid nutrition element IDs: ${invalidIds.join(', ')}`);
    }
  }

  private async validateDiets(dietIds: bigint[]) {
    if (!dietIds?.length) return;

    const diets = await this.prisma.diet.findMany({
      where: {
        id: { in: dietIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const foundIds = diets.map(d => d.id);
    const invalidIds = dietIds.filter(id => !foundIds.includes(id));

    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid diet IDs: ${invalidIds.join(', ')}`);
    }
  }

  private async validateAllergies(allergyIds: bigint[]) {
    if (!allergyIds?.length) return;

    const allergies = await this.prisma.allergy.findMany({
      where: {
        id: { in: allergyIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const foundIds = allergies.map(a => a.id);
    const invalidIds = allergyIds.filter(id => !foundIds.includes(id));

    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid allergy IDs: ${invalidIds.join(', ')}`);
    }
  }

  private async validateAndFilterIds(ids: bigint[], type: 'diet' | 'allergy') {
    const validIds = ids.filter(id => id !== undefined);
    
    if (validIds.length === 0) {
      return [];
    }

    let items;
    if (type === 'diet') {
      items = await this.prisma.diet.findMany({
        where: {
          id: { in: validIds },
          deletedAt: null
        }
      });
    } else {
      items = await this.prisma.allergy.findMany({
        where: {
          id: { in: validIds },
          deletedAt: null
        }
      });
    }

    const foundIds = items.map(item => item.id);
    const notFoundIds = validIds.filter(id => !foundIds.includes(id));

    if (notFoundIds.length > 0) {
      throw new NotFoundException(
        `${type === 'diet' ? 'Diet' : 'Allergy'} with ID(s) ${notFoundIds.join(', ')} not found`
      );
    }

    return validIds;
  }

  private async findOrCreateIngredient(name_en: string, name_fr: string): Promise<bigint> {
    try {
      // First try exact match (case-insensitive)
      let existingIngredient = await this.prisma.ingredient.findFirst({
        where: {
          AND: [
            { name_en: { equals: name_en, mode: 'insensitive' } },
            { name_fr: { equals: name_fr, mode: 'insensitive' } },
            { deletedAt: null }
          ]
        }
      });

      if (existingIngredient) {
        return existingIngredient.id;
      }
      // Try partial match if exact match fails
      existingIngredient = await this.prisma.ingredient.findFirst({
        where: {
          AND: [
            {
              OR: [
                { name_en: { contains: name_en, mode: 'insensitive' } },
                { name_fr: { contains: name_fr, mode: 'insensitive' } }
              ]
            },
            { deletedAt: null }
          ]
        }
      });

      if (existingIngredient) {
        return existingIngredient.id;
      }

      // Create new ingredient if no match found
      const newIngredient = await this.prisma.ingredient.create({
        // uppercase first letter
        data: {
          name_en: name_en.charAt(0).toUpperCase() + name_en.slice(1),
          name_fr: name_fr.charAt(0).toUpperCase() + name_fr.slice(1)
        }
      });

      return newIngredient.id;
    } catch (error) {
      console.error('Error in findOrCreateIngredient:', error);
    }
  }

  async create(dto: CreateRecipeDto) {
    // Validate category exists
    const category = await this.prisma.category.findFirst({
      where: { id: dto.categoryId, deletedAt: null },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${dto.categoryId} not found`);
    }

    // Validate cuisine exists
    const cuisine = await this.prisma.cuisine.findFirst({
      where: { id: dto.cuisineId, deletedAt: null },
    });

    if (!cuisine) {
      throw new NotFoundException(`Cuisine with ID ${dto.cuisineId} not found`);
    }

    // Filter and validate diets
    const notPermittedDiets = dto.notPermittedDiets
      ? await this.validateAndFilterIds(dto.notPermittedDiets, 'diet')
      : [];

    // Filter and validate allergies
    const notPermittedAllergies = dto.notPermittedAllergies
      ? await this.validateAndFilterIds(dto.notPermittedAllergies, 'allergy')
      : [];

    // Process ingredients
    const processedIngredients = [];
    for (const ing of dto.ingredients) {
      let ingredientId = ing.ingredientId;

      // If names are provided instead of ID, find or create the ingredient
      if (!ingredientId && ing.name_en && ing.name_fr) {
        ingredientId = await this.findOrCreateIngredient(ing.name_en, ing.name_fr);
      }

      processedIngredients.push({
        ingredientId,
        quantity: ing.quantity,
        unitId: ing.unitId
      });
    }

    return this.prisma.recipe.create({
      data: {
        name_en: dto.name_en,
        name_fr: dto.name_fr,
        shortDescription_en: dto.shortDescription_en,
        shortDescription_fr: dto.shortDescription_fr,
        preparationTime: dto.preparationTime,
        cookingTime: dto.cookingTime,
        imageUrl: dto.imageUrl,
        portions: dto.portions,
        categoryId: dto.categoryId,
        cuisineId: dto.cuisineId,
        notPermittedDiets,
        notPermittedAllergies,
        difficultyLevel_en: dto.difficultyLevel_en,
        difficultyLevel_fr: dto.difficultyLevel_fr,
        ingredients: {
          create: processedIngredients
        },
        steps: {
          create: dto.steps.map(step => ({
            order: step.order,
            content_en: step.content_en,
            content_fr: step.content_fr
          }))
        },
        nutritionFacts: {
          create: dto.nutritionFacts.map(fact => ({
            nutritionElementId: fact.nutritionElementId,
            value: fact.value
          }))
        },
        links: {
          create: dto.links?.map(link => ({
            type: link.type,
            url: link.url
          }))
        }
      },
      include: {
        category: true,
        cuisine: true,
        ingredients: {
          include: {
            ingredient: true,
            unit: true
          }
        },
        steps: true,
        nutritionFacts: {
          include: {
            nutritionElement: true
          }
        },
        links: true
      }
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    categoryIds?: string[];
    difficultyLevel?: DifficultyLevel;
    order?: 'asc' | 'desc';
    diets?: string[];
    allergies?: string[];
    cuisineIds?: string[];
    onlyDeleted?: boolean | null;
  }, userId?: bigint) {
    const {
      page = 1,
      limit = 10,
      search,
      categoryIds,
      difficultyLevel,
      diets,
      allergies,
      cuisineIds,
      onlyDeleted
    } = query;

    // Generate a daily random seed
    const now = new Date();
    const dailySeed = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate();

    const where: Prisma.RecipeWhereInput = {
      AND: [
        ...(onlyDeleted ? [{ deletedAt: { not: null } }] : [{ deletedAt: null }]),
        ...(search ? [{
          OR: [
            { name_en: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
            { name_fr: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
            { shortDescription_en: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
            { shortDescription_fr: { contains: search, mode: 'insensitive' as Prisma.QueryMode } }
          ]
        }] : []),
        ...(categoryIds?.length > 0 ? [{
          categoryId: {
            in: categoryIds.map(toBigInt)
          }
        }] : []),
        ...(cuisineIds?.length > 0 ? [{
          cuisineId: {
            in: cuisineIds.map(toBigInt)
          }
        }] : []),
        ...(difficultyLevel ? [{ difficultyLevel_en: difficultyLevel }] : []),
        ...(diets?.length > 0 ? [{
          NOT: {
            notPermittedDiets: {
              hasSome: diets.map(toBigInt)
            }
          }
        }] : []),
        ...(allergies?.length > 0 ? [{
          NOT: {
            notPermittedAllergies: {
              hasSome: allergies.map(toBigInt)
            }
          }
        }] : [])
      ]
    };

    const total = await this.prisma.recipe.count({ where });

    const recipes = await this.prisma.recipe.findMany({
      where,
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        shortDescription_en: true,
        shortDescription_fr: true,
        preparationTime: true,
        cookingTime: true,
        portions: true,
        cuisineId: true,
        categoryId: true,
        difficultyLevel_en: true,
        difficultyLevel_fr: true,
        imageUrl: true,
        category: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            // description_en: true,
            // description_fr: true,
            // image: true
          }
        },
        cuisine: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            image: true
          }
        },
        ingredients: {
          select: {
            ingredient: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                type_en: true,
                type_fr: true,
                description_en: true,
                description_fr: true,
                image: true
              }
            },
            quantity: true,
            unit: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                symbol: true
              }
            }
          }
        },
        steps: {
          select: {
            order: true,
            content_en: true,
            content_fr: true
          },
          orderBy: {
            order: 'asc'
          }
        },
        nutritionFacts: {
          select: {
            nutritionElement: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                unit: true
              }
            },
            value: true
          }
        },
        _count: {
          select: {
            likes: true,
            wishlists: true,
            views: true
          }
        },

        likes: true,
        wishlists: true
      },
      orderBy: [
        {
          id: 'asc'
        }
      ],
      skip: (page - 1) * limit,
      take: limit
    });

    // Create a deterministic but random-looking order that changes daily
    let seed = dailySeed;
    const shuffledRecipes = [...recipes].sort(() => {
      seed++;
      return (Math.sin(seed) * 10000) - 0.5;
    });

    const transformedItems = shuffledRecipes.map(recipe => ({
      ...recipe,
      likesCount: recipe._count.likes,
      wishlistsCount: recipe._count.wishlists,
      viewsCount: recipe._count.views,
      _count: undefined,
      isLiked: recipe.likes.some(like => like.userId === userId),
      isInWishlist: recipe.wishlists.some(wishlist => wishlist.userId === userId),
    }));

    return {
      items: transformedItems,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint, userId?: bigint) {
    const recipe = await this.prisma.recipe.findFirst({
      where: { 
        id,
        deletedAt: null 
      },
      include: {
        category: true,
        ingredients: {
          include: {
            ingredient: true,
            unit: true
          }
        },
        steps: true,
        nutritionFacts: {
          include: {
            nutritionElement: true
          }
        },
        links: true,
        _count: {
          select: {
            likes: true,
            wishlists: true,
            views: true
          }
        },
        likes: true,
        wishlists: true
      }
    });

    if (!recipe) {
      throw new NotFoundException(`Recipe with ID ${id} not found`);
    }

    // Destructure to remove likes and wishlists from the response
    const { likes, wishlists, _count, ...recipeData } = recipe;

    return {
      ...recipeData,
      isLiked: likes.some(like => like.userId === userId),
      isInWishlist: wishlists.some(wishlist => wishlist.userId === userId),
      likesCount: _count.likes,
      wishlistsCount: _count.wishlists,
      viewsCount: _count.views
    };
  }

  async update(id: bigint, dto: UpdateRecipeDto) {
    const recipe = await this.prisma.recipe.findUnique({
      where: { id },
      include: {
        ingredients: true,
        steps: true,
        nutritionFacts: true,
        links: true
      }
    });

    if (!recipe) {
      throw new NotFoundException(`Recipe with ID ${id} not found`);
    }

    // Validate category if provided
    if (dto.categoryId) {
      const category = await this.prisma.category.findFirst({
        where: { id: dto.categoryId, deletedAt: null }
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${dto.categoryId} not found`);
      }
    }

    // Validate cuisine if provided
    if (dto.cuisineId) {
      const cuisine = await this.prisma.cuisine.findFirst({
        where: { id: dto.cuisineId, deletedAt: null }
      });

      if (!cuisine) {
        throw new NotFoundException(`Cuisine with ID ${dto.cuisineId} not found`);
      }
    }

    // Filter and validate diets if provided
    const notPermittedDiets = dto.notPermittedDiets
      ? await this.validateAndFilterIds(dto.notPermittedDiets, 'diet')
      : undefined;

    // Filter and validate allergies if provided
    const notPermittedAllergies = dto.notPermittedAllergies
      ? await this.validateAndFilterIds(dto.notPermittedAllergies, 'allergy')
      : undefined;

    return this.prisma.recipe.update({
      where: { id },
      data: {
        name_en: dto.name_en,
        name_fr: dto.name_fr,
        shortDescription_en: dto.shortDescription_en,
        shortDescription_fr: dto.shortDescription_fr,
        preparationTime: dto.preparationTime,
        cookingTime: dto.cookingTime,
        imageUrl: dto.imageUrl,
        portions: dto.portions,
        deletedAt: dto.deletedAt,
        category: dto.categoryId ? {
          connect: {
            id: dto.categoryId
          }
        } : undefined,
        cuisine: dto.cuisineId ? {
          connect: {
            id: dto.cuisineId
          }
        } : undefined,
        notPermittedDiets,
        notPermittedAllergies,
        difficultyLevel_en: dto.difficultyLevel_en,
        difficultyLevel_fr: dto.difficultyLevel_fr,
        ingredients: dto.ingredients ? {
          deleteMany: {},
          create: dto.ingredients.map(ing => ({
            ingredientId: ing.ingredientId,
            quantity: ing.quantity,
            unitId: ing.unitId
          }))
        } : undefined,
        steps: dto.steps ? {
          deleteMany: {},
          create: dto.steps.map(step => ({
            order: step.order,
            content_en: step.content_en,
            content_fr: step.content_fr
          }))
        } : undefined,
        nutritionFacts: dto.nutritionFacts ? {
          deleteMany: {},
          create: dto.nutritionFacts.map(fact => ({
            nutritionElementId: fact.nutritionElementId,
            value: fact.value
          }))
        } : undefined,
        links: dto.links ? {
          deleteMany: {},
          create: dto.links.map(link => ({
            type: link.type,
            url: link.url
          }))
        } : undefined
      },
      include: {
        category: true,
        cuisine: true,
        ingredients: {
          include: {
            ingredient: true,
            unit: true
          }
        },
        steps: true,
        nutritionFacts: {
          include: {
            nutritionElement: true
          }
        },
        links: true
      }
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.recipe.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }

  async getLatestRecipes(userId?: bigint) {
    const recipes = await this.prisma.recipe.findMany({
      where: {
        deletedAt: null,
        isActive: true,
      },
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        shortDescription_en: true,
        shortDescription_fr: true,
        preparationTime: true,
        portions: true,
        difficultyLevel_en: true,
        difficultyLevel_fr: true,
        category: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            description_en: true,
            description_fr: true,
            image: true
          }
        },
        cuisine: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            image: true
          }
        },
        ingredients: {
          select: {
            ingredient: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                type_en: true,
                type_fr: true,
                description_en: true,
                description_fr: true,
                image: true
              }
            },
            quantity: true,
            unit: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                symbol: true
              }
            }
          }
        },
        steps: {
          select: {
            order: true,
            content_en: true,
            content_fr: true
          },
          orderBy: {
            order: 'asc'
          }
        },
        nutritionFacts: {
          select: {
            nutritionElement: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                unit: true
              }
            },
            value: true
          }
        },
        links: {
          select: {
            type: true,
            url: true
          }
        },
        _count: {
          select: {
            likes: true,
            wishlists: true,
            views: true
          }
        },
        likes: true,
        wishlists: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    return recipes.map((recipe: RecipeWithCounts) => ({
      ...recipe,
      liked: recipe.likes.some(like => like.userId === userId),
      likesCount: recipe._count?.likes || 0,
      wishlisted: recipe.wishlists.some(wishlist => wishlist.userId === userId),
      viewsCount: recipe._count?.views || 0,
      isLiked: recipe.likes.some(like => like.userId === userId),
      isInWishlist: recipe.wishlists.some(wishlist => wishlist.userId === userId),
      likes: undefined,
      wishlists: undefined,
      _count: undefined
    }));
  }

  async likeRecipe(recipeId: bigint, userId: bigint) {
    // Check if recipe exists
    await this.findOne(recipeId);

    // Check if already liked
    const existingLike = await this.prisma.recipeLike.findFirst({
      where: {
        userId,
        recipeId
      }
    });

    if (existingLike) {
      throw new ConflictException('Recipe already liked');
    }

    // Create like
    return this.prisma.recipeLike.create({
      data: {
        userId,
        recipeId
      }
    });
  }

  async unlikeRecipe(recipeId: bigint, userId: bigint) {
    // Check if recipe exists
    await this.findOne(recipeId);

    // Find the like
    const like = await this.prisma.recipeLike.findFirst({
      where: {
        userId,
        recipeId
      }
    });

    if (!like) {
      throw new NotFoundException('Like not found');
    }

    // Hard delete the like
    return this.prisma.recipeLike.delete({
      where: { id: like.id }
    });
  }

  async addToWishlist(recipeId: bigint, userId: bigint) {
    // Check if recipe exists
    await this.findOne(recipeId);

    // Check if already in wishlist
    const existing = await this.prisma.recipeWishlist.findFirst({
      where: {
        userId,
        recipeId
      }
    });

    if (existing) {
      throw new ConflictException('Recipe already in wishlist');
    }

    // Add to wishlist
    return this.prisma.recipeWishlist.create({
      data: {
        userId,
        recipeId
      }
    });
  }

  async removeFromWishlist(recipeId: bigint, userId: bigint) {
    // Check if recipe exists
    await this.findOne(recipeId);

    // Find the wishlist entry
    const wishlist = await this.prisma.recipeWishlist.findFirst({
      where: {
        userId,
        recipeId
      }
    });

    if (!wishlist) {
      throw new NotFoundException('Recipe not found in wishlist');
    }

    // Remove from wishlist
    return this.prisma.recipeWishlist.delete({
      where: { id: wishlist.id }
    });
  }

  async getWishlistedRecipes(userId: bigint, query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc'
    } = query;

    // Calculate skip
    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.RecipeWhereInput = {
      deletedAt: null,
      isActive: true,
      wishlists: {
        some: {
          userId
        }
      },
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' as const } },
          { name_fr: { contains: search, mode: 'insensitive' as const } },
          { shortDescription_en: { contains: search, mode: 'insensitive' as const } },
          { shortDescription_fr: { contains: search, mode: 'insensitive' as const } }
        ]
      })
    };

    // Get total count
    const total = await this.prisma.recipe.count({ where });

    // Get recipes
    const recipes = await this.prisma.recipe.findMany({
      where,
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        shortDescription_en: true,
        shortDescription_fr: true,
        preparationTime: true,
        portions: true,
        difficultyLevel_en: true,
        difficultyLevel_fr: true,
        imageUrl: true,
        category: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            description_en: true,
            description_fr: true,
            image: true
          }
        },
        // cuisine: {
        //   select: {
        //     id: true,
        //     name_en: true,
        //     name_fr: true,
        //     image: true
        //   }
        // },
        ingredients: {
          select: {
            ingredient: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                description_en: true,
                description_fr: true,
                image: true
              }
            },
            quantity: true,
            unit: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                symbol: true
              }
            }
          }
        },
        steps: {
          select: {
            order: true,
            content_en: true,
            content_fr: true
          },
          orderBy: {
            order: 'asc'
          }
        },
        nutritionFacts: {
          select: {
            nutritionElement: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                unit: true
              }
            },
            value: true
          }
        },
        links: {
          select: {
            type: true,
            url: true
          }
        },
        _count: {
          select: {
            likes: true,
            views: true
          }
        },
        likes: {
          where: {
            userId
          },
          take: 1,
          select: {
            id: true
          }
        }
      },
      orderBy: { createdAt: order },
      skip,
      take: limit
    });

    return {
      data: recipes.map(recipe => ({
        ...recipe,
        liked: recipe.likes.length > 0,
        likesCount: recipe._count.likes,
        wishlisted: true,
        viewsCount: recipe._count.views || 0,
        likes: undefined,
        _count: undefined
      })),
      meta: {
        total,
        page,
        limit,
        hasNextPage: skip + limit < total,
        hasPreviousPage: page > 1
      }
    };
  }

  async addView(recipeId: bigint, userId: bigint) {
    // Check if recipe exists
    await this.findOne(recipeId);

    // Create view record
    return this.prisma.recipeView.create({
      data: {
        userId,
        recipeId
      }
    });
  }

  async getMostPopularRecipes(userId?: bigint) {
    const recipes = await this.prisma.recipe.findMany({
      where: {
        deletedAt: null,
        isActive: true
      },
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        shortDescription_en: true,
        shortDescription_fr: true,
        preparationTime: true,
        portions: true,
        difficultyLevel_en: true,
        difficultyLevel_fr: true,
        category: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            description_en: true,
            description_fr: true,
            image: true
          }
        },
        cuisine: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            image: true
          }
        },
        ingredients: {
          select: {
            ingredient: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                type_en: true,
                type_fr: true,
                description_en: true,
                description_fr: true,
                image: true
              }
            },
            quantity: true,
            unit: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                symbol: true
              }
            }
          }
        },
        steps: {
          select: {
            order: true,
            content_en: true,
            content_fr: true
          },
          orderBy: {
            order: 'asc'
          }
        },
        nutritionFacts: {
          select: {
            nutritionElement: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                unit: true
              }
            },
            value: true
          }
        },
        links: {
          select: {
            type: true,
            url: true
          }
        },
        _count: {
          select: {
            likes: true,
            wishlists: true,
            views: true
          }
        },
        ...(userId ? {
          likes: {
            where: {
              userId: userId
            },
            take: 1,
            select: {
              id: true
            }
          },
          wishlists: {
            where: {
              userId: userId
            },
            take: 1,
            select: {
              id: true
            }
          }
        } : {
          likes: {
            take: 0,
            select: {
              id: true
            }
          },
          wishlists: {
            take: 0,
            select: {
              id: true
            }
          }
        })
      },
      orderBy: {
        views: {
          _count: 'desc'
        }
      },
      take: 10
    });

    return recipes.map(({ _count, likes, wishlists, ...recipe }) => ({
      ...recipe,
      liked: likes?.length > 0,
      likesCount: _count?.likes || 0,
      wishlisted: wishlists?.length > 0,
      viewsCount: _count?.views || 0,
      likes: undefined,
      wishlists: undefined,
      _count: undefined,
      
    }));
  }

  async getRandomRecipes(query: { page?: number; limit?: number, userId?: bigint }) {
    const { page = 1, limit = 10, userId } = query;
    const offset = (page - 1) * limit;

    // Get a count of all recipes
    const totalRecipes = await this.prisma.recipe.count();

    // Get random IDs within the range
    const randomIds = Array.from({ length: limit }, () => 
      Math.floor(Math.random() * totalRecipes) + 1
    );

    // Get the recipes
    const recipes = await this.prisma.recipe.findMany({
      where: {
        id: {
          in: randomIds
        }
      },
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        shortDescription_en: true,
        shortDescription_fr: true,
        preparationTime: true,
        cookingTime: true,
        portions: true,
        difficultyLevel_en: true,
        difficultyLevel_fr: true,
        imageUrl: true,
        category: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            description_en: true,
            description_fr: true,
            image: true
          }
        },
        cuisine: {
          select: {
            id: true,
            name_en: true,
            name_fr: true,
            image: true
          }
        },
        ingredients: {
          select: {
            ingredient: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                type_en: true,
                type_fr: true,
                description_en: true,
                description_fr: true,
                image: true
              }
            },
            quantity: true,
            unit: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                symbol: true
              }
            }
          }
        },
        steps: {
          select: {
            order: true,
            content_en: true,
            content_fr: true
          },
          orderBy: {
            order: 'asc'
          }
        },
        nutritionFacts: {
          select: {
            nutritionElement: {
              select: {
                id: true,
                name_en: true,
                name_fr: true,
                unit: true
              }
            },
            value: true
          }
        },
        links: {
          select: {
            type: true,
            url: true
          }
        },
        _count: {
          select: {
            likes: true,
            wishlists: true,
            views: true
          }
        },
        ...(userId ? {
          likes: {
            select: {
              id: true,
              userId: true
            }
          },
          wishlists: {
            select: {
              id: true,
              userId: true
            }
          }
        } : {
          likes: {
  
            select: {
              id: true,
              userId: true
            }
          },
          wishlists: {
  
            select: {
              id: true,
              userId: true
            }
          }
        })
      }
    });
    // Add views for each recipe if userId is provided
    if (userId) {
      try {
        // Create views sequentially instead of all at once
        for (const recipe of recipes) {
          try {
            await this.prisma.recipeView.create({
              data: {
                userId,
                recipeId: recipe.id
              }
            });
          } catch (error) {
            console.error(`Failed to create view for recipe ${recipe.id}:`, error);
            // Continue with other recipes even if one fails
            continue;
          }
        }
      } catch (error) {
        console.error('Error in view creation process:', error);
        // Continue with the response even if view creation fails
      }
    }

    const transformedItems = recipes.map(recipe => ({
      ...recipe,
      likesCount: recipe._count.likes,
      wishlistsCount: recipe._count.wishlists,
      viewsCount: recipe._count.views,
      _count: undefined,
      isLiked: recipe.likes.some(like => like.userId === userId),
      isInWishlist: recipe.wishlists.some(wishlist => wishlist.userId === userId),
    }));

    return {
      items: transformedItems,
      total: totalRecipes,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < totalRecipes,
      hasPreviousPage: page > 1
    };
  }

  public async cleanupPDFFile(filePath: string) {
    try {
      await fs.promises.unlink(filePath);
    } catch (err) {
      console.error('Error deleting temporary PDF:', err);
    }
  }

  async generateRecipePDF(id: bigint): Promise<{ filename: string, filePath: string }> {
    const recipe = await this.findOne(id);
    if (!recipe) {
      throw new NotFoundException(`Recipe with ID ${id} not found`);
    }

    const doc = new PDFDocument({
      size: 'A4',
      margin: 50
    });

    const pdfDir = path.join(process.cwd(), 'uploads', 'pdfs');
    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir, { recursive: true });
    }

    const filename = `recipe-${id}-${Date.now()}.pdf`;
    const filePath = path.join(pdfDir, filename);
    const writeStream = fs.createWriteStream(filePath);

    doc.pipe(writeStream);

    // Left side content
    const leftColumnX = 50;
    const rightColumnX = 300;

    // Title and description at the top
    doc.fontSize(24)
       .font('Helvetica-Bold')
       .text(recipe.name_fr, { align: 'left' });

    if (recipe.shortDescription_fr) {
      doc.moveDown(0.5)
         .fontSize(12)
         .font('Helvetica')
         .text(recipe.shortDescription_fr, {
           width: 400,
           align: 'left'
         });
    }

    // Preparation info
    doc.moveDown(0.5)
       .fontSize(12)
       .text(`Préparation: ${recipe.preparationTime} minutes`)
       .text(`Cuisson: ${recipe.cookingTime} minutes`);

    // Left column - Ingredients
    doc.moveDown(1)
       .fontSize(14)
       .font('Helvetica-Bold')
       .text('Ingrédients', { continued: true })
       .fontSize(12)
       .font('Helvetica')
       .text(`    ${recipe.portions} portions`);

    doc.moveDown(0.5);
    recipe.ingredients.forEach(ing => {
      doc.text(`○ ${ing.quantity} ${ing.unit.name_fr} ${ing.ingredient.name_fr}`, {
        indent: 10,
        continued: false
      });
    });

    // Right column - Steps
    let yPosition = 250; // Starting position for steps
    recipe.steps.forEach((step, index) => {
      doc.fontSize(14)
         .font('Helvetica-Bold')
         .text(`Étape ${index + 1}`, rightColumnX, yPosition);

      yPosition += 20; // Move down for step content
      doc.fontSize(12)
         .font('Helvetica')
         .text(step.content_fr, rightColumnX, yPosition, {
           width: 250,
           align: 'left'
         });

      yPosition = doc.y + 20; // Add space between steps
    });

    // Footer


    doc.end();
    await new Promise<void>((resolve) => writeStream.on('finish', resolve));

    // Return a plain object with filename and filePath
    return {
      filename: filename,
      filePath: filePath
    };
  }

  async addReview(recipeId: bigint, userId: bigint, dto: CreateReviewDto) {
    // Check if recipe exists
    const recipe = await this.prisma.recipe.findFirst({
      where: {
        id: recipeId,
        deletedAt: null,
        isActive: true
      }
    });

    if (!recipe) {
      throw new NotFoundException(`Recipe with ID ${recipeId} not found`);
    }

    // Create the review
    return this.prisma.recipeReview.create({
      data: {
        userId,
        recipeId,
        review_title: dto.review_title,
        review_body: dto.review_body,
        status: 'OPEN'
      },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            avatar: true
          }
        }
      }
    });
  }
} 