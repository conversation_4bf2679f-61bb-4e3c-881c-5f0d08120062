import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { Prisma } from '@prisma/client';

export class BaseQueryDto {
  @ApiProperty({ 
    required: false,
    default: 1,
    minimum: 1
  })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ 
    required: false,
    default: 10,
    minimum: 1
  })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({ 
    required: false,
    enum: ['asc', 'desc'],
    default: 'desc'
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order?: Prisma.SortOrder = 'desc';
} 