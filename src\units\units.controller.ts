import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { UnitsService } from './units.service';
import { CreateUnitDto, UpdateUnitDto } from './dto/unit.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('Units')
@Controller('units')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class UnitsController {
  constructor(private readonly unitsService: UnitsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new unit (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Unit created successfully' })
  create(@Body() dto: CreateUnitDto) {
    return this.unitsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all units (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return all units' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted units. If false, returns only active units.'
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: string,
  ) {
    return this.unitsService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted: onlyDeleted === 'true'
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get unit by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the unit' })
  findOne(@Param('id') id: string) {
    return this.unitsService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update unit (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Unit updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateUnitDto) {
    return this.unitsService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete unit (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Unit deleted successfully' })
  remove(@Param('id') id: string) {
    return this.unitsService.remove(toBigInt(id));
  }
} 