import { mkdirSync } from 'fs';
import { join } from 'path';

export function ensureUploadsDirectory() {
  const paths = [
    join(process.cwd(), 'uploads', 'avatars'),
    join(process.cwd(), 'uploads', 'notifications')
  ];

  paths.forEach(path => {
    try {
      mkdirSync(path, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  });
} 