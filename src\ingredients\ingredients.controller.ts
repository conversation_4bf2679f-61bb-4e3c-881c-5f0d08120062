import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { IngredientsService } from './ingredients.service';
import { CreateIngredientDto, UpdateIngredientDto } from './dto/ingredient.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('Ingredients')
@Controller('ingredients')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class IngredientsController {
  constructor(private readonly ingredientsService: IngredientsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new ingredient (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Ingredient created successfully' })
  create(@Body() dto: CreateIngredientDto) {
    return this.ingredientsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all ingredients (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return all ingredients' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted ingredients. If false, returns only active ingredients.'
  })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: boolean,
  ) {
    return this.ingredientsService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get ingredient by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the ingredient' })
  findOne(@Param('id') id: string) {
    return this.ingredientsService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update ingredient (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Ingredient updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateIngredientDto) {
    return this.ingredientsService.update(toBigInt(id), dto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete ingredient (ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Ingredient deleted successfully' })
  remove(@Param('id') id: string) {
    return this.ingredientsService.remove(toBigInt(id));
  }
} 