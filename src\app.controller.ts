import { Controller, Get, Header } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('robots.txt')
  @Header('Content-Type', 'text/plain')
  @ApiOperation({ summary: 'Get robots.txt' })
  @ApiResponse({ status: 200, description: 'Returns robots.txt content' })
  getRobots(): string {
    return 'User-agent: *\nDisallow: /';
  }
}
