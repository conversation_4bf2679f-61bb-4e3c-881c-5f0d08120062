import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateGeneralSettingDto, UpdateGeneralSettingDto } from './dto/general-setting.dto';

@Injectable()
export class GeneralSettingsService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateGeneralSettingDto) {
    // Check if key already exists
    const exists = await this.prisma.generalSetting.findUnique({
      where: { key: dto.key }
    });

    if (exists) {
      throw new ConflictException(`Setting with key "${dto.key}" already exists`);
    }

    return this.prisma.generalSetting.create({
      data: dto
    });
  }

  async findAll() {
    return this.prisma.generalSetting.findMany({
      orderBy: { key: 'asc' }
    });
  }

  async findOne(id: bigint) {
    const setting = await this.prisma.generalSetting.findUnique({
      where: { id }
    });

    if (!setting) {
      throw new NotFoundException(`Setting with ID ${id} not found`);
    }

    return setting;
  }

  async findByKey(key: string) {
    const setting = await this.prisma.generalSetting.findUnique({
      where: { key }
    });

    if (!setting) {
      throw new NotFoundException(`Setting with key "${key}" not found`);
    }

    return setting;
  }

  async update(id: bigint, dto: UpdateGeneralSettingDto) {
    await this.findOne(id);

    return this.prisma.generalSetting.update({
      where: { id },
      data: dto
    });
  }

  async updateByKey(key: string, dto: UpdateGeneralSettingDto) {
    await this.findByKey(key);

    return this.prisma.generalSetting.update({
      where: { key },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.generalSetting.delete({
      where: { id }
    });
  }
} 