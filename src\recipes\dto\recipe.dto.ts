import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, Min, ValidateNested, IsEnum, IsArray, IsDate } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { toBigInt } from '../../common/utils/bigint.util';
import { DifficultyLevel } from '../enums/difficulty-level.enum';
import { RecipeIngredientDto } from './recipe-ingredient.dto';
import { RecipeStepDto } from './recipe-step.dto';
import { RecipeNutritionDto } from './recipe-nutrition.dto';
import { LinkDto } from '../../links/dto/link.dto';

export class CreateRecipeDto {
  @ApiProperty({ example: 'Spaghetti Carbonara' })
  @IsString()
  name_en: string;

  @ApiProperty({ example: 'Spaghetti à la Carbonara' })
  @IsString()
  name_fr: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shortDescription_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shortDescription_fr?: string;

  @ApiProperty({ description: 'Preparation time in minutes' })
  @IsNumber()
  @Min(1)
  preparationTime: number;

  @ApiProperty({ description: 'Cooking time in minutes' })
  @IsNumber()
  @Min(1)
  cookingTime: number;

  @ApiProperty()
  @Transform(({ value }) => {
    const bigIntValue = toBigInt(value);
    if (bigIntValue === undefined) {
      throw new Error(`Invalid category ID format: ${value}`);
    }
    return bigIntValue;
  })
  categoryId: bigint;

  @ApiProperty()
  @Transform(({ value }) => {
    const bigIntValue = toBigInt(value);
    if (bigIntValue === undefined) {
      throw new Error(`Invalid cuisine ID format: ${value}`);
    }
    return bigIntValue;
  })
  cuisineId: bigint;

  @ApiProperty({ 
    type: [String],
    example: ['1', '2', '3'],
    description: 'Array of diet IDs that are not permitted for this recipe',
    required: false 
  })
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  notPermittedDiets?: bigint[];

  @ApiProperty({ 
    type: [String],
    example: ['1', '2', '3'],
    description: 'Array of allergy IDs that are not permitted for this recipe',
    required: false 
  })
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  notPermittedAllergies?: bigint[];

  @ApiProperty({ enum: DifficultyLevel })
  @IsEnum(DifficultyLevel)
  difficultyLevel_en: DifficultyLevel;

  @ApiProperty({ example: 'FACILE' })
  @IsString()
  difficultyLevel_fr: string;

  @ApiProperty({ type: [RecipeIngredientDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeIngredientDto)
  ingredients: RecipeIngredientDto[];

  @ApiProperty({ type: [RecipeStepDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeStepDto)
  steps: RecipeStepDto[];

  @ApiProperty({ type: [RecipeNutritionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeNutritionDto)
  nutritionFacts: RecipeNutritionDto[];

  @ApiProperty({ type: [LinkDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LinkDto)
  links: LinkDto[];

  @ApiProperty({ example: 4 })
  @IsNumber()
  @Min(1)
  portions: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

export class UpdateRecipeDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deletedAt?: Date | null;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shortDescription_en?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shortDescription_fr?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  preparationTime?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  cookingTime?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => value ? toBigInt(value) : undefined)
  categoryId?: bigint;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => value ? toBigInt(value) : undefined)
  cuisineId?: bigint;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  notPermittedDiets?: bigint[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => value?.map((id: string) => toBigInt(id)))
  notPermittedAllergies?: bigint[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(DifficultyLevel)
  difficultyLevel_en?: DifficultyLevel;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  difficultyLevel_fr?: string;

  @ApiProperty({ type: [RecipeIngredientDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeIngredientDto)
  ingredients?: RecipeIngredientDto[];

  @ApiProperty({ type: [RecipeStepDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeStepDto)
  steps?: RecipeStepDto[];

  @ApiProperty({ type: [RecipeNutritionDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipeNutritionDto)
  nutritionFacts?: RecipeNutritionDto[];

  @ApiProperty({ type: [LinkDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LinkDto)
  links?: LinkDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  portions?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;
} 