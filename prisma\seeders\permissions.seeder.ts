import { PrismaClient } from '@prisma/client';

export async function seedPermissions(prisma: PrismaClient) {
  const permissions = [
    // User permissions
    { name: 'users:read' },
    { name: 'users:create' },
    { name: 'users:update' },
    { name: 'users:delete' },
    { name: 'users:reset-password' },

    // Recipe permissions
    { name: 'recipes:read' },
    { name: 'recipes:create' },
    { name: 'recipes:update' },
    { name: 'recipes:delete' },

    // Category permissions
    { name: 'categories:read' },
    { name: 'categories:create' },
    { name: 'categories:update' },
    { name: 'categories:delete' },

    // Ingredient permissions
    { name: 'ingredients:read' },
    { name: 'ingredients:create' },
    { name: 'ingredients:update' },
    { name: 'ingredients:delete' },

    // Diet permissions
    { name: 'diets:read' },
    { name: 'diets:create' },
    { name: 'diets:update' },
    { name: 'diets:delete' },

    // Allergy permissions
    { name: 'allergies:read' },
    { name: 'allergies:create' },
    { name: 'allergies:update' },
    { name: 'allergies:delete' },

    // Unit permissions
    { name: 'units:read' },
    { name: 'units:create' },
    { name: 'units:update' },
    { name: 'units:delete' },

    // Nutrition element permissions
    { name: 'nutrition:read' },
    { name: 'nutrition:create' },
    { name: 'nutrition:update' },
    { name: 'nutrition:delete' }
  ];

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission
    });
  }

  // Assign permissions to roles
  const adminRole = await prisma.role.findUnique({
    where: { name: 'ADMIN' }
  });

  if (adminRole) {
    // Get all permissions
    const allPermissions = await prisma.permission.findMany();
    
    // Connect all permissions to ADMIN role
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        permissions: {
          connect: allPermissions.map(p => ({ id: p.id }))
        }
      }
    });
  }

  console.log('Permissions seeded successfully');
} 