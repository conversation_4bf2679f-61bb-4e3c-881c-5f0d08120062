import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateNutritionElementDto, UpdateNutritionElementDto } from './dto/nutrition-element.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class NutritionElementsService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateNutritionElementDto) {
    const exists = await this.prisma.nutritionElement.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en
          ? `Nutrition element with English name "${dto.name_en}" already exists`
          : `Nutrition element with French name "${dto.name_fr}" already exists`
      );
    }

    return this.prisma.nutritionElement.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.NutritionElementWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.nutritionElement.count({ where });

    const nutritionElements = await this.prisma.nutritionElement.findMany({
      where,
      orderBy: { createdAt: order },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      items: nutritionElements,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const element = await this.prisma.nutritionElement.findFirst({
      where: { 
        id,
        deletedAt: null
      }
    });

    if (!element) {
      throw new NotFoundException(`Nutrition element with ID ${id} not found`);
    }

    return element;
  }

  async update(id: bigint, dto: UpdateNutritionElementDto) {
    const element = await this.prisma.nutritionElement.findFirst({
      where: { id }
    });

    if (!element) {
      throw new NotFoundException(`Nutrition element with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr) {
      const exists = await this.prisma.nutritionElement.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en
            ? `Nutrition element with English name "${dto.name_en}" already exists`
            : `Nutrition element with French name "${dto.name_fr}" already exists`
        );
      }
    }

    return this.prisma.nutritionElement.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.nutritionElement.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }
} 