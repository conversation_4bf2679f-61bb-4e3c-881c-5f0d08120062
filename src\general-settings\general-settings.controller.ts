import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { GeneralSettingsService } from './general-settings.service';
import { CreateGeneralSettingDto, UpdateGeneralSettingDto } from './dto/general-setting.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';

@ApiTags('General Settings')
@Controller('general-settings')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class GeneralSettingsController {
  constructor(private readonly generalSettingsService: GeneralSettingsService) {}

  @Post()
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new setting (SUPER_ADMIN)' })
  @ApiResponse({ status: 201, description: 'Setting created successfully' })
  create(@Body() dto: CreateGeneralSettingDto) {
    return this.generalSettingsService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all settings (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return all settings' })
  findAll() {
    return this.generalSettingsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get setting by id (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the setting' })
  findOne(@Param('id') id: string) {
    return this.generalSettingsService.findOne(toBigInt(id));
  }

  @Get('key/:key')
  @ApiOperation({ summary: 'Get setting by key (USER, ADMIN, SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Return the setting' })
  findByKey(@Param('key') key: string) {
    return this.generalSettingsService.findByKey(key);
  }

  @Patch(':id')
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update setting by id (SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Setting updated successfully' })
  update(@Param('id') id: string, @Body() dto: UpdateGeneralSettingDto) {
    return this.generalSettingsService.update(toBigInt(id), dto);
  }

  @Patch('key/:key')
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update setting by key (SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Setting updated successfully' })
  updateByKey(@Param('key') key: string, @Body() dto: UpdateGeneralSettingDto) {
    return this.generalSettingsService.updateByKey(key, dto);
  }

  @Delete(':id')
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete setting (SUPER_ADMIN)' })
  @ApiResponse({ status: 200, description: 'Setting deleted successfully' })
  remove(@Param('id') id: string) {
    return this.generalSettingsService.remove(toBigInt(id));
  }
} 