import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseBigIntPipe implements PipeTransform {
  transform(value: string, metadata: ArgumentMetadata) {
    try {
      if (typeof value === 'bigint') return value;
      if (typeof value === 'number') return BigInt(value);
      if (typeof value === 'string') {
        // Remove any non-numeric characters
        const cleaned = value.replace(/[^0-9]/g, '');
        if (!cleaned) {
          throw new Error('Invalid bigint value');
        }
        return BigInt(cleaned);
      }
      throw new Error('Invalid bigint value');
    } catch (error) {
      throw new BadRequestException(
        `Validation failed (bigint is expected): ${value}`
      );
    }
  }
} 