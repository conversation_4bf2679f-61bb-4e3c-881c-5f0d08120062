import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDietDto, UpdateDietDto } from './dto/diet.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class DietsService {
  constructor(private prisma: PrismaService) {}

  private async validateIngredients(ingredientIds: bigint[]) {
    if (!ingredientIds?.length) return;

    const ingredients = await this.prisma.ingredient.findMany({
      where: {
        id: { in: ingredientIds },
        deletedAt: null
      },
      select: { id: true }
    });

    const foundIds = ingredients.map(i => i.id);
    const invalidIds = ingredientIds.filter(id => !foundIds.includes(id));

    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid ingredient IDs: ${invalidIds.join(', ')}`);
    }
  }

  async create(dto: CreateDietDto) {
    const exists = await this.prisma.diet.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en
          ? `Diet with English name "${dto.name_en}" already exists`
          : `Diet with French name "${dto.name_fr}" already exists`
      );
    }

    return this.prisma.diet.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.DietWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } },
          { description_en: { contains: search, mode: 'insensitive' } },
          { description_fr: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.diet.count({ where });

    const diets = await this.prisma.diet.findMany({
      where,
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        description_en: true,
        description_fr: true,
        image: true,
        bannedIngredients: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true
      },
      orderBy: { id: order },
      skip: (page - 1) * limit,
      take: limit
    });

    for (const diet of diets) {
      if (diet.bannedIngredients?.length) {
        const ingredients = await this.prisma.ingredient.findMany({
          where: {
            id: { in: diet.bannedIngredients },
            deletedAt: null
          },
          select: {
            id: true,
            name_en: true,
            name_fr: true
          }
        });
        diet.bannedIngredients = ingredients as any;
      }
    }

    return {
      items: diets,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const diet = await this.prisma.diet.findFirst({
      where: { 
        id,
        deletedAt: null 
      },
      select: {
        id: true,
        name_en: true,
        name_fr: true,
        description_en: true,
        description_fr: true,
        image: true,
        bannedIngredients: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true
      }
    });

    if (!diet) {
      throw new NotFoundException(`Diet with ID ${id} not found`);
    }

    if (diet.bannedIngredients?.length) {
      const ingredients = await this.prisma.ingredient.findMany({
        where: {
          id: { in: diet.bannedIngredients },
          deletedAt: null
        },
        select: {
          id: true,
          name_en: true,
          name_fr: true
        }
      });
      diet.bannedIngredients = ingredients as any;
    }

    return diet;
  }

  async update(id: bigint, dto: UpdateDietDto) {
    const diet = await this.prisma.diet.findFirst({
      where: { id }
    });

    if (!diet) {
      throw new NotFoundException(`Diet with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr) {
      const exists = await this.prisma.diet.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en
            ? `Diet with English name "${dto.name_en}" already exists`
            : `Diet with French name "${dto.name_fr}" already exists`
        );
      }
    }

    return this.prisma.diet.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.diet.update({
      where: { id },
      data: { 
        deletedAt: new Date() 
      }
    });
  }
} 