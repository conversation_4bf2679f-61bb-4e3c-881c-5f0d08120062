import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { PrismaModule } from './prisma/prisma.module';
import { UploadModule } from './common/modules/upload.module';
import { UnitsModule } from './units/units.module';
import { DietsModule } from './diets/diets.module';
import { AllergiesModule } from './allergies/allergies.module';
import { CategoriesModule } from './categories/categories.module';
import { IngredientsModule } from './ingredients/ingredients.module';
import { RecipesModule } from './recipes/recipes.module';
import { NutritionElementsModule } from './nutrition-elements/nutrition-elements.module';
import { GeneralSettingsModule } from './general-settings/general-settings.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NotificationsModule } from './notifications/notifications.module';
import { CuisinesModule } from './cuisines/cuisines.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    AuthModule,
    UsersModule,
    PrismaModule,
    UploadModule,
    UnitsModule,
    DietsModule,
    AllergiesModule,
    CategoriesModule,
    IngredientsModule,
    RecipesModule,
    NutritionElementsModule,
    GeneralSettingsModule,
    NotificationsModule,
    CuisinesModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
