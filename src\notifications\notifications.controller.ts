import { Controller, Post, Body, UseGuards, Get, Query } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiOperation, ApiBody, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { FirebaseService } from '../common/services/firebase.service';
import { SendNotificationDto } from './dto/send-notification.dto';
import { ResendNotificationDto } from './dto/resend-notification.dto';
import { GetNotificationsDto } from './dto/get-notifications.dto';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class NotificationsController {
  constructor(private readonly firebaseService: FirebaseService) {}

  @Post('send')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Send notification to users (ADMIN, SUPER_ADMIN)' })
  @ApiBody({ type: SendNotificationDto })
  async sendNotification(@Body() dto: SendNotificationDto) {
    return this.firebaseService.sendNotificationToUsers({
      title: dto.title,
      description: dto.description,
      image: dto.image,
      users: dto.users,
      all: dto.all
    });
  }

  @Post('resend')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Resend existing notification by ID (ADMIN, SUPER_ADMIN)' })
  @ApiBody({ type: ResendNotificationDto })
  async resendNotification(@Body() dto: ResendNotificationDto) {
    return this.firebaseService.resendNotification(dto.id);
  }

  @Get()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get all notifications with pagination (ADMIN, SUPER_ADMIN)' })
  async getNotifications(@Query() query: GetNotificationsDto) {
    return this.firebaseService.getNotifications(query);
  }
} 