import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { CuisinesService } from './cuisines.service';
import { CreateCuisineDto, UpdateCuisineDto, CuisineQueryDto } from './dto/cuisine.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/roles.enum';
import { toBigInt } from '../common/utils/bigint.util';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Cuisines')
@Controller('cuisines')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class CuisinesController {
  constructor(private readonly cuisinesService: CuisinesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new cuisine' })
  @ApiResponse({ status: 201, description: 'The cuisine has been successfully created.' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('image'))
  create(
    @Body() createCuisineDto: CreateCuisineDto,
    @UploadedFile() image: Express.Multer.File,
  ) {
    if (image) {
      createCuisineDto.image = image;
    }
    return this.cuisinesService.create(createCuisineDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all cuisines' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ 
    name: 'onlyDeleted', 
    required: false, 
    type: Boolean,
    description: 'If true, returns only deleted cuisines. If false, returns only active cuisines.'
  })
  @ApiResponse({ status: 200, description: 'Return all cuisines.' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('order') order?: 'asc' | 'desc',
    @Query('onlyDeleted') onlyDeleted?: string,
  ) {
    return this.cuisinesService.findAll({
      page: page ? parseInt(page) : undefined,
      limit: limit ? parseInt(limit) : undefined,
      search,
      order,
      onlyDeleted: onlyDeleted === 'true'
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a cuisine by id' })
  @ApiResponse({ status: 200, description: 'Return the cuisine.' })
  @ApiResponse({ status: 404, description: 'Cuisine not found.' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.cuisinesService.findOne(toBigInt(id));
  }

  @Patch(':id')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Update a cuisine' })
  @ApiResponse({ status: 200, description: 'The cuisine has been successfully updated.' })
  @ApiResponse({ status: 404, description: 'Cuisine not found.' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('image'))
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCuisineDto: UpdateCuisineDto,
    @UploadedFile() image: Express.Multer.File,
  ) {
    if (image) {
      updateCuisineDto.image = image;
    }
    return this.cuisinesService.update(toBigInt(id), updateCuisineDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Delete a cuisine' })
  @ApiResponse({ status: 200, description: 'The cuisine has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Cuisine not found.' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.cuisinesService.remove(toBigInt(id));
  }
} 