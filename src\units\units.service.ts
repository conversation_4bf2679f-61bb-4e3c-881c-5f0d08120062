import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUnitDto, UpdateUnitDto } from './dto/unit.dto';
import { Prisma, UnitType } from '@prisma/client';

@Injectable()
export class UnitsService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateUnitDto) {
    // Check if unit with same names or symbol exists
    const exists = await this.prisma.unit.findFirst({
      where: {
        OR: [
          { name_en: dto.name_en },
          { name_fr: dto.name_fr },
          { symbol: dto.symbol }
        ],
        deletedAt: null
      }
    });

    if (exists) {
      throw new ConflictException(
        exists.name_en === dto.name_en 
          ? `Unit with English name "${dto.name_en}" already exists`
          : exists.name_fr === dto.name_fr
          ? `Unit with French name "${dto.name_fr}" already exists`
          : `Unit with symbol "${dto.symbol}" already exists`
      );
    }

    return this.prisma.unit.create({
      data: dto
    });
  }

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    order?: 'asc' | 'desc';
    onlyDeleted?: boolean;
  }) {
    const {
      page = 1,
      limit = 10,
      search,
      order = 'desc',
      onlyDeleted = false
    } = query;

    const where: Prisma.UnitWhereInput = {
      deletedAt: onlyDeleted === true ? { not: null } : null,
      ...(search && {
        OR: [
          { name_en: { contains: search, mode: 'insensitive' } },
          { name_fr: { contains: search, mode: 'insensitive' } },
          { symbol: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const total = await this.prisma.unit.count({ where });

    const units = await this.prisma.unit.findMany({
      where,
      orderBy: { createdAt: order },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      items: units,
      total,
      page,
      limit,
      hasNextPage: (page - 1) * limit + limit < total,
      hasPreviousPage: page > 1
    };
  }

  async findOne(id: bigint) {
    const unit = await this.prisma.unit.findFirst({
      where: { 
        id,
        deletedAt: null
      }
    });

    if (!unit) {
      throw new NotFoundException(`Unit with ID ${id} not found`);
    }

    return unit;
  }

  async update(id: bigint, dto: UpdateUnitDto) {
    const unit = await this.prisma.unit.findFirst({
      where: { id }
    });

    if (!unit) {
      throw new NotFoundException(`Unit with ID ${id} not found`);
    }

    if (dto.name_en || dto.name_fr || dto.symbol) {
      const exists = await this.prisma.unit.findFirst({
        where: {
          OR: [
            dto.name_en ? { name_en: dto.name_en } : {},
            dto.name_fr ? { name_fr: dto.name_fr } : {},
            dto.symbol ? { symbol: dto.symbol } : {}
          ],
          id: { not: id }
        }
      });

      if (exists) {
        throw new ConflictException(
          exists.name_en === dto.name_en 
            ? `Unit with English name "${dto.name_en}" already exists`
            : exists.name_fr === dto.name_fr
            ? `Unit with French name "${dto.name_fr}" already exists`
            : `Unit with symbol "${dto.symbol}" already exists`
        );
      }
    }

    return this.prisma.unit.update({
      where: { id },
      data: dto
    });
  }

  async remove(id: bigint) {
    await this.findOne(id);
    
    return this.prisma.unit.update({
      where: { id },
      data: { 
        deletedAt: new Date()
      }
    });
  }
} 