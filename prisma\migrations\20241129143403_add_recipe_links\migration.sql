-- CreateEnum
CREATE TYPE "LinkType" AS ENUM ('LINK', 'IMAGE', 'VIDEO_URL');

-- CreateTable
CREATE TABLE "links" (
    "id" BIGSERIAL NOT NULL,
    "type" "LinkType" NOT NULL,
    "url" TEXT NOT NULL,
    "recipeId" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "links_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "links" ADD CONSTRAINT "links_recipeId_fkey" FOREIGN KEY ("recipeId") REFERENCES "recipes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
