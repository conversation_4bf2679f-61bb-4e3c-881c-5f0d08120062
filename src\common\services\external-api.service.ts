import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

interface ExternalUserResponse {
  fullname?: string;
  email?: string;
  message?: string;
}

@Injectable()
export class ExternalApiService {
  constructor(private readonly httpService: HttpService) {}

  async findUserByPhone(phone: string): Promise<ExternalUserResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get<ExternalUserResponse>(
          `http://localhost/index.php?phone=${phone}`
        )
      );
      return data;
    } catch (error) {
      return { message: 'not found' };
    }
  }
} 